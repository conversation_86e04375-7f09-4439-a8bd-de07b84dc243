import pandas as pd
import json
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import openai
from pathlib import Path
import sys
import os
from datetime import datetime
from dotenv import load_dotenv

class StrictMarkdownPromptEngine:
    @staticmethod
    def create_individual_summary_prompt(summaries: List[str], entities_list: List[str], reasoning_list: List[str], business_context: str = "") -> str:
        summaries_text = "\n\n".join([f"ANALYSIS {i+1}: {summary}" for i, summary in enumerate(summaries)])
        entities_text = "\n".join([f"ENTITIES {i+1}: {entities}" for i, entities in enumerate(entities_list) if entities])
        reasoning_text = "\n\n".join([f"REASONING {i+1}: {reasoning}" for i, reasoning in enumerate(reasoning_list) if reasoning])

        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""

        return f"""
You are a Yazaki supply chain analyst. You MUST output ONLY markdown format with bullet points.

STRICT MARKDOWN OUTPUT REQUIREMENTS:
- You MUST start with "## Individual Analysis"
- You MUST use ONLY bullet points with "•" symbol
- You MUST use markdown bold formatting with "**" for key terms
- You MUST have exactly 3 bullet points maximum
- Each bullet point MUST be 15 words or less
- If insufficient data, output: "• **Insufficient Data**: Cannot generate meaningful analysis without additional context"

REQUIRED MARKDOWN FORMAT:
## Individual Analysis

• **Key Finding**: [Main insight about component demand/supply]
• **Impact**: [Effect on production planning or customer fulfillment]
• **Action Required**: [Specific next steps for supply chain team]

YAZAKI-SPECIFIC FOCUS:
- Focus on automotive component demand patterns, supply chain performance, and fulfillment metrics
- Use Yazaki terminology: components, harnesses, connectors, assemblies, production lines
- Emphasize customer relationships (Tesla, Rivian, etc.) and part numbers when available
- Address supply chain visibility, demand signals, and production planning specifics

CONVERSATION DATA:
{summaries_text}

{entities_text if entities_text else ""}

{reasoning_text if reasoning_text else ""}
{context_section}

CRITICAL: Output ONLY the markdown format as specified above. Do not include any other text, explanations, or formatting.
"""

    @staticmethod
    def create_session_summary_prompt(individual_summaries: List[str], session_id: str, business_context: str = "") -> str:
        summaries_text = "\n\n".join([f"SUMMARY {i+1}:\n{summary}" for i, summary in enumerate(individual_summaries)])
        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""

        return f"""
You are a Yazaki supply chain director. You MUST output ONLY markdown format with bullet points.

STRICT MARKDOWN OUTPUT REQUIREMENTS:
- You MUST start with "## Supply Chain Analysis Summary"
- You MUST use ONLY bullet points with "•" symbol
- You MUST use markdown bold formatting with "**" for key terms
- You MUST have exactly 3 bullet points maximum
- Each bullet point MUST be 20 words or less
- If insufficient data, output: "• **Data Limitation**: Analysis incomplete due to insufficient context in source conversations"

REQUIRED MARKDOWN FORMAT:
## Supply Chain Analysis Summary

• **Primary Issue**: [Main supply chain challenge or opportunity]
• **Customer Impact**: [Specific effect on key automotive customers]
• **Recommended Action**: [Executive decision required for supply chain optimization]

EXECUTIVE SYNTHESIS REQUIREMENTS:
- Consolidate individual summaries into cohesive supply chain insights
- Focus on Yazaki's automotive component business impact
- Highlight customer-specific issues (Tesla, Rivian, etc.) and part number analysis
- Address demand forecasting, production planning, and fulfillment performance

INDIVIDUAL SUMMARIES:
{summaries_text}
{context_section}

CRITICAL: Output ONLY the markdown format as specified above. Do not include any other text, explanations, or formatting.
"""

    @staticmethod
    def create_title_generation_prompt(main_summary: str, session_id: str, business_context: str = "") -> str:
        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""

        return f"""
Generate a concise title for this Yazaki supply chain analysis session.

TITLE REQUIREMENTS:
- 3-6 words maximum
- Include customer name (Tesla, Rivian, etc.) when relevant
- Focus on supply chain action: Analysis, Forecast, Optimization, Review
- Use Yazaki terminology: Components, Demand, Fulfillment, Supply

EXAMPLES:
- "Tesla Component Demand Analysis"
- "Rivian Supply Chain Review"
- "Component Forecast Optimization"

SESSION DATA:
{main_summary}
{context_section}

CRITICAL: Output ONLY the title. Do not include any other text, explanations, or formatting.
"""

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SessionSummary:
    session_id: str
    individual_summaries: List[str]
    main_summary: str
    title: str
    message_count: int
    processing_time: float
    total_input_tokens: int
    total_output_tokens: int
    individual_input_tokens: List[int]
    individual_output_tokens: List[int]
    session_summary_input_tokens: int
    session_summary_output_tokens: int
    title_input_tokens: int
    title_output_tokens: int

class LLMClient:
    def __init__(self, api_key: str, model_name: str = "gpt-4.1-nano-2025-04-14"):
        self.api_key = api_key
        self.model_name = model_name
        self.client = openai.OpenAI(api_key=api_key)
        self.prompt_engine = StrictMarkdownPromptEngine()

    def count_tokens(self, prompt: str) -> int:
        return len(prompt.split()) * 1.3

    def generate_content(self, prompt: str, max_retries: int = 3) -> Tuple[str, int, int]:
        input_tokens = self.count_tokens(prompt)

        for attempt in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=4000,
                    temperature=0.7
                )

                output_tokens = response.usage.completion_tokens if response.usage else 0
                input_tokens = response.usage.prompt_tokens if response.usage else input_tokens

                logger.info(f"Token usage - Input: {input_tokens}, Output: {output_tokens}")
                return response.choices[0].message.content, input_tokens, output_tokens

            except Exception as e:
                logger.error(f"Error generating content (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2)

class AdvancedPromptEngine:
    @staticmethod
    def create_individual_summary_prompt(reasoning_list: List[str], entities_list: List[str]) -> str:
        return f"""
You are a senior business intelligence analyst with extensive expertise in analytical reasoning assessment and strategic insight generation. Your responsibility is to analyze complex business reasoning while capturing specific entities (names, products, companies, technologies, etc.) to produce comprehensive analytical summaries that preserve concrete details.

COMPREHENSIVE ANALYTICAL REASONING AND ENTITY CAPTURE PROTOCOL:

STEP 1: SPECIFIC ENTITY IDENTIFICATION AND REASONING CONTEXT MAPPING
Extract and preserve all specific named entities from the reasoning content:
- Identify specific people names, job titles, roles, and decision-makers mentioned in the reasoning chain
- Capture exact product names, service names, brand names, model numbers, and technology platforms referenced
- Extract company names, organization names, department names, business units, and team designations
- Note specific technology names, software platforms, systems, tools, and methodologies used in reasoning
- Identify location names, geographic regions, market segments, facilities, and operational areas
- Capture financial figures, metrics, percentages, dates, timelines, and quantitative data used in analysis
- Extract project names, initiative titles, program designations, and strategic effort names
- Note regulatory frameworks, compliance standards, governance structures, and policy references
- Identify competitor names, vendor names, partner organizations, and external stakeholders
- Capture specific processes, methodologies, frameworks, analytical tools, and strategic approaches mentioned

STEP 2: REASONING CHAIN ANALYSIS WITH ENTITY PRESERVATION
Examine analytical reasoning while preserving specific entity details:
- Analyze complete reasoning chain identifying core business logic, analytical methodology, and decision-making framework mentioning specific entities by name
- Map sequential thought processes, cause-and-effect relationships, logical connections, and strategic dependencies involving named entities
- Identify analytical tools, evaluation models, assessment criteria, and decision frameworks mentioning specific methodologies, software, or systems by name
- Extract key assumptions, constraints, risk considerations, and success factors affecting particular entities, products, or organizational units
- Assess quality, relevance, depth, and comprehensiveness of supporting evidence referencing specific data sources, research studies, benchmarks, or analytical tools
- Evaluate thoroughness of alternative options, trade-off analyses, and scenario planning involving specific technologies, vendors, approaches, or strategic options
- Analyze strategic coherence, logical consistency, implementation feasibility, and business alignment referencing particular systems, processes, or organizational capabilities
- Identify gaps, weaknesses, inconsistencies, or areas requiring additional analysis affecting specific entities, projects, or initiatives
- Assess analytical rigor, strategic soundness, execution viability, and competitive positioning involving particular technologies, products, or market positions
- Evaluate how reasoning addresses business objectives, market conditions, competitive landscape mentioning specific goals, competitors, market segments, or strategic targets

STEP 3: STAKEHOLDER ECOSYSTEM ANALYSIS WITH CONCRETE ENTITY DETAILS
Conduct detailed analysis preserving entity relationships and specific information:
- Map organizational hierarchy, decision-making authority, influence levels, and strategic importance mentioning specific roles, titles, departments, or individuals
- Analyze relationships, dependencies, interactions, collaboration requirements, and integration needs involving named systems, teams, processes, or technologies
- Assess each entity's role in business ecosystem including specific decision-makers, influencers, implementers, beneficiaries, and affected parties
- Evaluate entity-specific capabilities, resources, constraints, skill requirements, capacity limitations, and development needs referencing particular competencies, systems, or organizational assets
- Identify communication requirements, coordination mechanisms, change management implications, and adoption readiness involving specific teams, stakeholders, or organizational units
- Map entity relationships that could accelerate solution implementation, create synergies, competitive advantages, or pose implementation challenges with concrete examples
- Assess how entity dynamics contribute to strategic objectives, competitive positioning, market leadership, and value creation mentioning specific initiatives, products, or capabilities
- Evaluate entity-specific benefits, costs, risks, implementation requirements, and success factors with actual figures, timelines, or resource requirements
- Analyze collective entity ecosystem impact on strategic success, competitive advantage, market positioning, and organizational transformation involving particular strategic outcomes or competitive advantages
- Consider long-term sustainability, scalability, and competitive positioning of entity relationships mentioning specific technologies, partnerships, or strategic assets

STEP 4: INTEGRATED BUSINESS LOGIC AND STRATEGIC COHERENCE SYNTHESIS WITH ENTITY SPECIFICITY
Combine reasoning and entity analysis preserving concrete details:
- Synthesize analytical reasoning with specific entity information creating comprehensive business understanding that includes names, products, and quantitative details
- Assess how reasoning framework addresses specific entity needs, constraints, capabilities, and strategic objectives with concrete examples
- Evaluate alignment between analytical conclusions and particular entity relationships, dependencies, and strategic interactions
- Identify synergies between reasoning recommendations and specific entity strengths, opportunities, technologies, or competitive advantages
- Analyze potential conflicts, tensions, or misalignments between reasoning outcomes and particular entity interests, capabilities, or strategic priorities
- Map implementation pathways that leverage specific entity strengths while addressing named challenges, constraints, or stakeholder concerns
- Assess strategic viability of reasoning conclusions considering specific entity realities, market conditions, competitive factors, and organizational capabilities
- Evaluate risk factors, success enablers, and critical dependencies involving particular entities, technologies, partnerships, or strategic assets
- Identify optimization opportunities that enhance both analytical outcomes and specific entity performance with concrete improvement suggestions
- Consider long-term sustainability, scalability, and competitive positioning involving particular technologies, partnerships, strategic assets, or competitive advantages

STEP 5: STRATEGIC BUSINESS IMPACT EVALUATION WITH QUANTITATIVE AND ENTITY DETAILS
Determine comprehensive business implications preserving specific information:
- Assess strategic significance, competitive impact, and market positioning implications mentioning specific competitive advantages, market positions, or strategic outcomes
- Identify quantitative benefits including specific cost savings amounts, revenue improvements, efficiency gains, and competitive advantage creation with actual figures
- Evaluate qualitative benefits such as risk reduction, compliance enhancement, innovation acceleration, and capability development involving particular technologies, processes, or competencies
- Calculate resource requirements, investment needs, implementation costs, and total cost considerations with specific budget figures, timelines, or resource allocations
- Estimate return on investment, value creation potential, competitive advantage sustainability, and long-term strategic benefit with concrete projections
- Assess implementation feasibility, timeline realism, resource allocation requirements, and execution complexity involving specific systems, teams, or organizational capabilities
- Identify critical success factors, key performance indicators, monitoring requirements, and accountability frameworks mentioning specific metrics, targets, or responsible parties
- Evaluate market timing considerations, competitive response anticipation, and strategic positioning optimization involving particular market conditions, competitor actions, or timing factors
- Consider change management requirements, organizational readiness, cultural alignment, and adoption facilitation referencing specific training programs, change initiatives, or organizational adjustments
- Assess overall strategic coherence, business viability, competitive sustainability, and market leadership potential involving particular strategic assets, competitive advantages, or market positions

INPUT DATA FOR COMPREHENSIVE ANALYTICAL ASSESSMENT:
REASONING: {reasoning_list[0]}
ENTITIES: {entities_list[0]}

CRITICAL WRITING AND OUTPUT REQUIREMENTS:

ENTITY SPECIFICITY AND CONCRETE DETAILS MANDATE:
- Include specific names, products, companies, technologies, and quantitative data throughout the summary
- Reference actual entities mentioned in the reasoning by their exact names
- Preserve financial figures, percentages, dates, and other concrete data points used in analysis
- Mention specific people, roles, departments, and organizational units when referenced in reasoning
- Include exact product names, service offerings, technology platforms, and system names
- Reference particular competitors, vendors, partners, and market segments by name
- Preserve specific project names, initiative titles, and program designations
- Include actual metrics, KPIs, targets, and performance indicators with specific values

CONTENT AND ANALYTICAL STRUCTURE REQUIREMENTS:
- Generate exactly one continuous paragraph with no line breaks, bullet points, or paragraph separations
- Maintain strict adherence to 200 words maximum while preserving specific entity details
- Include most significant business insight, primary strategic implication, key stakeholder consideration, and important quantitative measure with actual names and numbers
- Incorporate reasoning validation and entity relationship analysis while preserving concrete details
- Focus on business-critical insights while maintaining entity specificity
- Use clear, accessible language that includes concrete details and specific entity names
- Ensure every sentence contributes strategic insight while preserving entity specificity
- Emphasize strategic significance, competitive implications, market positioning, and implementation viability with specific examples

PROFESSIONAL TONE WITH ENTITY PRESERVATION:
- Maintain professional analytical tone while preserving all specific entity details
- Write with authoritative confidence including concrete names, products, and data points
- Use business language that naturally incorporates specific entity references and quantitative details
- Ensure content flows as cohesive analytical narrative with preserved entity specificity

MANDATORY MARKDOWN FORMATTING REQUIREMENTS:
- You MUST format your response using markdown bullet points
- Start each key point with a bullet point (-)
- Use clear, concise bullet points that capture the most important insights
- Ensure each bullet point is a complete, standalone insight
- Maintain professional tone while using bullet point format
- Focus on actionable business insights and strategic implications
- Keep bullet points concise but informative
- Preserve all specific entity names, products, and quantitative data in bullet format

Execute the complete five-step analytical protocol internally and then provide only the final business analytical summary with specific entity details as specified above.
"""

    @staticmethod
    def create_session_summary_prompt(individual_summaries: List[str], session_id: str) -> str:
        summaries_text = "\n\n".join([f"SUMMARY {i+1}:\n{summary}" for i, summary in enumerate(individual_summaries)])

        return f"""
You are a senior strategic consultant with extensive experience in multi-faceted business intelligence synthesis and executive strategic reporting. Your expertise lies in taking complex analytical insights containing specific entities and distilling them into coherent, actionable strategic guidance that preserves concrete details and names.

COMPREHENSIVE STRATEGIC INTELLIGENCE SYNTHESIS PROTOCOL WITH ENTITY PRESERVATION:

STEP 1: MULTI-SOURCE ANALYTICAL INTELLIGENCE DECOMPOSITION WITH ENTITY EXTRACTION
Extract and preserve all specific entities across all analytical summaries:
- Parse each summary to extract specific names, products, companies, technologies, and quantitative data from analytical insights
- Recognize recurring entities, analytical subjects, people, and organizations across multiple analytical summaries
- Extract financial figures, metrics, percentages, performance data, and analytical results with exact values
- Identify specific analytical methodologies, frameworks, tools, and approaches mentioned by name
- Capture technology platforms, software systems, analytical tools, and methodologies used in analysis
- Recognize market segments, geographic regions, customer groups, and competitive entities in analytical context
- Note specific analytical projects, research initiatives, studies, and analytical efforts with their actual names
- Extract regulatory frameworks, compliance requirements, and governance structures mentioned in analysis
- Identify vendors, data sources, analytical partners, and external organizations by name
- Capture analytical objectives, KPIs, targets, and success metrics with specific details

STEP 2: STRATEGIC ANALYTICAL FRAMEWORK INTEGRATION WITH CONCRETE DETAILS
Develop unified strategic intelligence preserving entity specifics:
- Synthesize individual analytical insights mentioning specific names, products, technologies, and organizations throughout
- Identify overarching analytical challenges, strategic opportunities, competitive threats, or analytical transformations affecting particular entities
- Map logical progression of analytical reasoning, strategic decisions, implementation approaches, and competitive advantage development involving named entities
- Analyze cause-and-effect relationships between analytical conclusions, business areas, capabilities, and positioning referencing specific analytical findings
- Assess how individual analytical insights contribute to broader strategic objectives, competitive differentiation, and market leadership mentioning particular initiatives, technologies, or analytical outcomes
- Rank analytical insights by strategic importance, implementation urgency, competitive impact, and value creation potential with specific priorities
- Identify critical analytical dependencies, sequencing requirements, optimization opportunities, and execution acceleration factors involving named projects or initiatives
- Evaluate resource allocation implications, investment decisions, capability development needs, and transformation requirements with specific details
- Map risk mitigation strategies, contingency planning, compliance considerations, and competitive response mechanisms involving particular entities or analytical risks
- Assess analytical scalability, sustainability, competitive differentiation maintenance, and market expansion capabilities referencing specific technologies, analytical capabilities, or competitive advantages

STEP 3: STAKEHOLDER ECOSYSTEM AND ORGANIZATIONAL IMPACT ASSESSMENT WITH ENTITY DETAILS
Analyze organizational dimensions preserving specific entity information:
- Identify strategic initiatives, analytical transformations, competitive positioning changes, and market initiatives mentioning specific programs, analytical capabilities, or organizational changes
- Assess organizational change requirements, analytical capability development needs, cultural transformation elements, and strategic skill advancement referencing particular teams, training programs, or analytical competencies
- Map communication strategies, engagement approaches, change management implications, and adoption mechanisms involving specific stakeholders, analytical teams, or organizational units
- Evaluate decision-making authority distribution, analytical approval processes, governance requirements, and accountability frameworks mentioning specific roles, committees, or oversight bodies
- Assess organizational analytical readiness, transformation capacity, change absorption capability, and success enablement referencing particular departments, analytical systems, or processes
- Aggregate quantitative strategic benefits including specific cost savings amounts, revenue improvements, efficiency gains, and competitive advantage creation with actual figures
- Evaluate qualitative strategic benefits such as analytical capability enhancement, competitive positioning improvement, and innovation acceleration involving particular technologies or analytical initiatives
- Calculate total strategic investment requirements including analytical resources, human capital, and infrastructure with specific details
- Estimate comprehensive strategic impact, return on analytical investment, value creation potential, and market leadership sustainability with concrete projections
- Identify quick analytical wins versus long-term strategic investments mentioning specific initiatives, timelines, or analytical milestones

STEP 4: EXECUTIVE STRATEGIC DECISION FRAMEWORK WITH CONCRETE ACTION ITEMS
Create actionable strategic guidance preserving entity specificity:
- Combine individual analytical recommendations mentioning specific initiatives, technologies, analytical capabilities, and organizational changes
- Identify optimal implementation sequencing, phasing requirements, milestone dependencies, and acceleration factors with actual timelines and analytical deliverables
- Map integration points, analytical dependencies, coordination mechanisms, and optimization strategies involving named systems, teams, or analytical processes
- Assess feasibility constraints, risk mitigation approaches, success probability factors, and optimization opportunities with specific examples
- Develop resource allocation plans, timeline strategies, performance monitoring systems, and adjustment mechanisms mentioning particular metrics, targets, or responsible analytical teams
- Distill key strategic decisions requiring executive attention referencing specific analytical proposals, investments, or strategic directions
- Identify critical success factors, performance monitoring requirements, and accountability frameworks with concrete KPIs, analytical targets, and oversight mechanisms
- Map timing considerations, opportunity windows, and positioning optimization involving specific market conditions, competitive factors, or analytical opportunities
- Provide clear immediate actions, executive priorities, and momentum building with specific next steps, responsible analytical teams, and deliverables
- Establish alignment maintenance, position defense, and advantage sustainability involving particular competitive advantages, analytical capabilities, or market positions

INPUT DATA FOR COMPREHENSIVE STRATEGIC SYNTHESIS:
{summaries_text}

CRITICAL WRITING AND OUTPUT REQUIREMENTS:

ENTITY SPECIFICITY AND CONCRETE DETAILS MANDATE:
- Include specific names, products, companies, technologies, and quantitative data throughout the summary
- Reference actual entities mentioned in the analytical summaries by their exact names
- Preserve financial figures, percentages, dates, and other concrete data points from analysis
- Mention specific people, roles, departments, and organizational units when available
- Include exact product names, service offerings, technology platforms, and analytical system names
- Reference particular competitors, vendors, partners, and market segments by name
- Preserve specific project names, initiative titles, and analytical program designations
- Include actual metrics, KPIs, targets, and performance indicators with specific values

STRATEGIC CONTENT AND ANALYTICAL STRUCTURE REQUIREMENTS:
- Generate exactly one continuous paragraph with no line breaks, bullet points, or paragraph separations
- Maintain strict adherence to 200 words maximum while preserving specific entity details
- Include critical analytical business theme, strategic stakeholders, competitive decisions, and measurable impact with actual names and numbers
- Focus on strategic analytical significance and actionable insights while maintaining entity specificity
- Highlight analytical transformation opportunities, competitive advantages, and value creation potential with concrete examples
- Ensure every sentence delivers executive-level strategic value while preserving entity details
- Emphasize analytical transformation opportunities, advantage development, and value creation mechanisms with specific references

EXECUTIVE TONE WITH ENTITY PRESERVATION:
- Use professional executive strategic summary tone while preserving all specific entity details
- Write with authoritative confidence including concrete names, products, and data points
- Maintain natural conversational strategic flow that incorporates specific entity references and quantitative details
- Ensure content represents unified strategic analytical intelligence with preserved entity specificity

Execute the complete four-step strategic analytical synthesis protocol internally and then provide only the final executive strategic analytical summary with specific entity details as specified above.
"""

    @staticmethod
    def create_title_generation_prompt(main_summary: str, session_id: str) -> str:
        return f"""
You are an expert strategic information architect who specializes in analytical content synthesis and precise title generation for executive-level strategic documentation. Your expertise lies in distilling complex analytical business content containing specific entities into concise, meaningful titles that immediately communicate analytical value and include key entity references.

COMPREHENSIVE STRATEGIC ANALYTICAL TITLE GENERATION PROTOCOL WITH ENTITY INCLUSION:

STEP 1: DEEP ANALYTICAL CONTENT ANALYSIS AND ENTITY EXTRACTION
Identify primary entities and analytical themes within the summary:
- Parse complete analytical summary content to identify specific products, technologies, companies, people, analytical tools, and initiatives mentioned
- Extract central analytical business challenge, strategic market opportunity, competitive analytical threat, or organizational analytical transformation objective involving particular entities
- Identify specific type of analytical business activity including strategic analytical planning, operational analytical optimization, or analytical market analysis mentioning key entities
- Determine organizational analytical scope and impact level referencing specific departments, business units, analytical teams, or market segments
- Assess strategic analytical impact level while considering specific technologies, analytical products, or organizational changes mentioned
- Recognize primary analytical stakeholders, business analytical units, and organizational analytical levels by their actual names or designations
- Map geographic analytical scope, temporal analytical context, and analytical market conditions involving specific regions, timeframes, or market segments
- Extract industry analytical sector specifics, business analytical function details, and analytical market segment considerations with concrete entity references
- Evaluate analytical urgency level, analytical timeline sensitivity, and analytical implementation priority mentioning specific deadlines, milestones, or phases
- Identify regulatory analytical, compliance analytical, or governance analytical context affecting particular entities, frameworks, or requirements

STEP 2: STRATEGIC ANALYTICAL SIGNIFICANCE AND BUSINESS OUTCOME EVALUATION WITH ENTITY FOCUS
Assess strategic analytical importance while preserving entity specifics:
- Determine strategic analytical importance, competitive analytical significance, and market analytical impact involving specific entities, products, or analytical initiatives
- Identify measurable analytical business outcomes including financial analytical performance, operational analytical efficiency, and competitive analytical advantage with specific metrics
- Evaluate analytical innovation elements, analytical transformation aspects, and analytical capability development involving particular technologies, systems, or analytical processes
- Assess analytical risk mitigation effectiveness, analytical opportunity capture potential, and analytical performance enhancement with concrete examples
- Extract future-state analytical vision, strategic analytical objectives, and analytical target outcomes mentioning specific goals, KPIs, or success criteria
- Classify primary type of strategic analytical business decision, organizational analytical transformation, or market analytical positioning initiative with entity details
- Determine strategic analytical decision-making level including operational analytical tactics, strategic analytical initiatives, or analytical industry leadership with specific scope
- Identify whether analytical focus is on strategic analytical assessment, analytical planning, analytical execution, or analytical optimization involving particular entities or processes
- Assess whether analytical discussion represents reactive analytical problem-solving or proactive strategic analytical opportunity development with specific context
- Evaluate sustainable analytical competitive advantage potential and long-term analytical value creation involving particular capabilities or market positions

STEP 3: STRATEGIC ANALYTICAL TITLE CONSTRUCTION WITH ENTITY INTEGRATION
Create optimal strategic analytical title incorporating key entities:
- Extract 2-3 most important strategic analytical concepts including specific entity names, products, technologies, or analytical tools when relevant
- Identify primary analytical business domain, strategic analytical area, or functional analytical focus that should lead title including key entity references
- Select most appropriate analytical action verb, strategic analytical process descriptor, or business analytical outcome indicator that captures essence while including entity context
- Choose specific, meaningful analytical business terminology that incorporates actual entity names, product names, technology references, or analytical tool names when relevant
- Ensure analytical concepts are concrete, immediately recognizable to executives, and include specific entity details when they add analytical value
- Use natural, conversational analytical business language that incorporates specific entity references without overcrowding
- Balance strategic analytical clarity with entity specificity to maintain immediate comprehension and memorability
- Choose active, specific analytical terminology that naturally incorporates entity references when they enhance understanding
- Ensure strategic analytical title distinguishes this specific discussion from similar topics through entity specificity when relevant
- Optimize for immediate analytical comprehension, memorability, and strategic relevance while including key entity identifiers when appropriate

ENTITY INTEGRATION AND ANALYTICAL TITLE AUTHENTICITY REQUIREMENTS:
- Include specific entity names, products, technologies, or analytical tools in the title when they are central to the strategic analytical discussion
- Balance entity specificity with title clarity and executive scannability
- Use entity references that immediately communicate the analytical focus and business context
- Ensure entity inclusion enhances rather than clutters the title's strategic analytical message
- Prioritize the most strategically significant entities that executives would recognize as key to the analytical discussion
- Maintain natural business language flow while incorporating specific entity references
- Avoid generic analytical terminology when specific entity names would provide clearer strategic context
- Include entity details that would help executives immediately understand the analytical scope and focus

SESSION SUMMARY FOR COMPREHENSIVE STRATEGIC ANALYTICAL ANALYSIS:
{main_summary}

CRITICAL STRATEGIC ANALYTICAL TITLE OUTPUT REQUIREMENTS:
- Execute complete three-step strategic analytical analysis protocol internally before generating analytical title
- Generate analytical title that immediately communicates strategic analytical business value and includes key entity references when relevant
- Maintain exactly 4-8 words maximum length while incorporating entity specificity when it adds strategic analytical value
- Use natural, professional analytical business language that includes specific entity references when they enhance clarity
- Include primary analytical business domain and key strategic analytical action with entity context when relevant to strategic understanding
- Ensure analytical uniqueness and specificity through entity inclusion when appropriate
- Focus on what would be most meaningful and strategically valuable while incorporating key entity identifiers
- Make analytical title immediately scannable, memorable, and strategically relevant with entity context when beneficial

Provide only the final strategic analytical title in plain text without quotation marks, punctuation, explanations, formatting, or additional commentary after completing your comprehensive strategic analytical analysis.
"""

class SessionAnalyzer:
    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client
        self.prompt_engine = StrictMarkdownPromptEngine()

    def load_and_preprocess_data(self, file_path: str) -> pd.DataFrame:
        logger.info(f"Loading data from {file_path}")

        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            df = pd.read_excel(file_path)
        else:
            df = pd.read_csv(file_path)

        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        df = df[df['role'] != 'human']

        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df

    def extract_entities(self, meta_string: str) -> str:
        try:
            if pd.isna(meta_string) or meta_string == '':
                return "No entities available"
            meta_data = json.loads(meta_string)
            entities = meta_data.get('entities', [])
            if isinstance(entities, list) and entities:
                return ", ".join(str(entity) for entity in entities)
            return "No entities available"
        except (json.JSONDecodeError, TypeError):
            return "No entities available"

    def extract_reasoning(self, message: str) -> str:
        try:
            if pd.isna(message) or message == '':
                return "No message available"
            message_data = json.loads(message)
            return message_data.get('reasoning', message)
        except (json.JSONDecodeError, TypeError):
            return message

    def extract_message_fields(self, message_string: str) -> Dict[str, str]:
        try:
            if pd.isna(message_string) or message_string == '':
                return {"summary": "No summary available", "reasoning": "No reasoning available"}

            try:
                message_data = json.loads(message_string)
                summary = message_data.get('summary', message_data.get('output', ''))
                reasoning = message_data.get('reasoning', '')
                entities = message_data.get('entities', '')

                return {
                    "summary": summary if summary else "No summary available",
                    "reasoning": reasoning if reasoning else "No reasoning available",
                    "entities": entities if entities else "No entities available"
                }
            except json.JSONDecodeError:
                lines = message_string.strip().split('\n')
                summary = ""
                reasoning = ""

                current_field = None
                for line in lines:
                    line = line.strip()
                    if line.lower().startswith('summary:') or line.lower().startswith('output:'):
                        current_field = 'summary'
                        summary = line.split(':', 1)[1].strip() if ':' in line else ""
                    elif line.lower().startswith('reasoning:'):
                        current_field = 'reasoning'
                        reasoning = line.split(':', 1)[1].strip() if ':' in line else ""
                    elif current_field == 'summary' and not line.lower().startswith('reasoning:'):
                        summary += " " + line
                    elif current_field == 'reasoning':
                        reasoning += " " + line

                return {
                    "summary": summary.strip() if summary.strip() else "No summary available",
                    "reasoning": reasoning.strip() if reasoning.strip() else "No reasoning available",
                    "entities": "No entities available"
                }

        except Exception as e:
            logger.error(f"Error extracting message fields: {e}")
            return {"summary": "No summary available", "reasoning": "No reasoning available", "entities": "No entities available"}

    def generate_individual_summary(self, row: pd.Series, business_context: str = "") -> Tuple[str, int, int]:
        message_fields = self.extract_message_fields(row.get('message', ''))

        prompt = self.prompt_engine.create_individual_summary_prompt(
            summaries=[message_fields['summary']],
            entities_list=[message_fields['entities']],
            reasoning_list=[message_fields['reasoning']],
            business_context=business_context
        )

        response, input_tokens, output_tokens = self.llm_client.generate_content(prompt)
        return response.strip(), input_tokens, output_tokens

    def generate_session_summary(self, individual_summaries: List[str], session_id: str, business_context: str = "") -> Tuple[str, int, int]:
        prompt = self.prompt_engine.create_session_summary_prompt(
            individual_summaries=individual_summaries,
            session_id=session_id,
            business_context=business_context
        )

        response, input_tokens, output_tokens = self.llm_client.generate_content(prompt)
        return response.strip(), input_tokens, output_tokens

    def generate_session_title(self, main_summary: str, session_id: str) -> Tuple[str, int, int]:
        prompt = self.prompt_engine.create_title_generation_prompt(
            main_summary=main_summary,
            session_id=session_id
        )

        response, input_tokens, output_tokens = self.llm_client.generate_content(prompt)
        return response.strip(), input_tokens, output_tokens

    def analyze_session(self, session_data: pd.DataFrame, session_id: str) -> SessionSummary:
        start_time = time.time()
        logger.info(f"Processing session {session_id} with {len(session_data)} messages")

        individual_summaries = []
        individual_input_tokens = []
        individual_output_tokens = []

        for idx, row in session_data.iterrows():
            summary, input_tokens, output_tokens = self.generate_individual_summary(row, "")
            individual_summaries.append(summary)
            individual_input_tokens.append(input_tokens)
            individual_output_tokens.append(output_tokens)
            time.sleep(0.3)

        logger.info(f"Generating main summary for session {session_id}")
        main_summary, session_input_tokens, session_output_tokens = self.generate_session_summary(individual_summaries, session_id, "")
        time.sleep(0.3)

        logger.info(f"Generating title for session {session_id}")
        title, title_input_tokens, title_output_tokens = self.generate_session_title(main_summary, session_id)

        processing_time = time.time() - start_time

        total_input_tokens = sum(individual_input_tokens) + session_input_tokens + title_input_tokens
        total_output_tokens = sum(individual_output_tokens) + session_output_tokens + title_output_tokens

        return SessionSummary(
            session_id=session_id,
            individual_summaries=individual_summaries,
            main_summary=main_summary,
            title=title,
            message_count=len(session_data),
            processing_time=processing_time,
            total_input_tokens=total_input_tokens,
            total_output_tokens=total_output_tokens,
            individual_input_tokens=individual_input_tokens,
            individual_output_tokens=individual_output_tokens,
            session_summary_input_tokens=session_input_tokens,
            session_summary_output_tokens=session_output_tokens,
            title_input_tokens=title_input_tokens,
            title_output_tokens=title_output_tokens
        )

    def analyze_session_with_context(self, session_data: pd.DataFrame, session_id: str, business_context: str) -> SessionSummary:
        start_time = time.time()
        logger.info(f"Processing session {session_id} with {len(session_data)} messages")

        individual_summaries = []
        individual_input_tokens = []
        individual_output_tokens = []

        for idx, row in session_data.iterrows():
            summary, input_tokens, output_tokens = self.generate_individual_summary(row, business_context)
            individual_summaries.append(summary)
            individual_input_tokens.append(input_tokens)
            individual_output_tokens.append(output_tokens)
            time.sleep(0.3)

        logger.info(f"Generating main summary for session {session_id}")
        main_summary, session_input_tokens, session_output_tokens = self.generate_session_summary(individual_summaries, session_id, business_context)
        time.sleep(0.3)

        logger.info(f"Generating title for session {session_id}")
        title, title_input_tokens, title_output_tokens = self.generate_session_title(main_summary, session_id)

        processing_time = time.time() - start_time

        total_input_tokens = sum(individual_input_tokens) + session_input_tokens + title_input_tokens
        total_output_tokens = sum(individual_output_tokens) + session_output_tokens + title_output_tokens

        return SessionSummary(
            session_id=session_id,
            individual_summaries=individual_summaries,
            main_summary=main_summary,
            title=title,
            message_count=len(session_data),
            processing_time=processing_time,
            total_input_tokens=total_input_tokens,
            total_output_tokens=total_output_tokens,
            individual_input_tokens=individual_input_tokens,
            individual_output_tokens=individual_output_tokens,
            session_summary_input_tokens=session_input_tokens,
            session_summary_output_tokens=session_output_tokens,
            title_input_tokens=title_input_tokens,
            title_output_tokens=title_output_tokens
        )

    def process_all_sessions(self, df: pd.DataFrame) -> List[SessionSummary]:
        sessions = df.groupby('session_id')
        results = []

        for session_id, session_data in sessions:
            try:
                session_summary = self.analyze_session(session_data, session_id)
                results.append(session_summary)
                logger.info(f"Completed session {session_id} in {session_summary.processing_time:.2f}s")
                logger.info(f"Token usage - Input: {session_summary.total_input_tokens}, Output: {session_summary.total_output_tokens}")
            except Exception as e:
                logger.error(f"Failed to process session {session_id}: {e}")
                continue

        return results

    def process_all_sessions_with_context(self, df: pd.DataFrame, business_context: str) -> List[SessionSummary]:
        sessions = df.groupby('session_id')
        results = []

        for session_id, session_data in sessions:
            try:
                session_summary = self.analyze_session_with_context(session_data, session_id, business_context)
                results.append(session_summary)
                logger.info(f"Completed session {session_id} in {session_summary.processing_time:.2f}s")
                logger.info(f"Token usage - Input: {session_summary.total_input_tokens}, Output: {session_summary.total_output_tokens}")
            except Exception as e:
                logger.error(f"Failed to process session {session_id}: {e}")
                continue

        return results

def load_api_key() -> str:
    load_dotenv()
    return os.getenv('OPENAI_API_KEY')
