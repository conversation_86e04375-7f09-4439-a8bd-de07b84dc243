import pandas as pd
import json
import time
import logging
from typing import List, Dict, Any
import sys
import os
from datetime import datetime
from pathlib import Path

import flow1
import flow2
import flow3
import flow4

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveNPTestProcessor:
    def __init__(self):
        self.api_key = self.load_api_key()

        if not self.api_key:
            raise ValueError("No API key found in .env file")

        logger.info("API key loaded successfully")

        # Define business context for each NP test file
        self.np_test_contexts = {
            "NP_test_01.csv": {
                "context": "Tesla demand and fulfillment trends analysis across 2023 and 2024. Focus on year-over-year order volume dynamics, total shipment activity, and anomalies between forecasted and actual orders for forecast alignment with Tesla planners.",
                "focus": "Tesla demand forecasting and shipment analysis",
                "purpose": "Support forecast alignment call with Tesla planning team"
            },
            "NP_test_02.csv": {
                "context": "Rivian R1S vehicle line supply performance and demand concentration assessment. Focus on identifying high-volume parts, sustained demand components, and fulfillment effectiveness analysis including detailed review of part PT00876922-E.",
                "focus": "Rivian R1S supply performance and key component analysis",
                "purpose": "Optimize supply chain for high-volume Rivian components"
            },
            "NP_test_03.csv": {
                "context": "Model 3 H LV carline demand landscape comprehensive analysis. Focus on historical customer ordering patterns, recent Tesla news impact, and external factors influencing demand or production planning.",
                "focus": "Model 3 H LV demand patterns and market influence analysis",
                "purpose": "Enhance demand planning with market intelligence integration"
            },
            "NP_test_04.csv": {
                "context": "Tesla recent order-to-fulfillment performance evaluation. Focus on demand conversion to shipments over time, identifying fulfillment inefficiencies or improvements in the supply chain process.",
                "focus": "Tesla order fulfillment performance optimization",
                "purpose": "Improve demand-to-shipment conversion rates"
            },
            "NP_test_05.csv": {
                "context": "SupplyWhy crystal ball projection forecast accuracy evaluation versus EDI demand signals for Cyber Truck HV carline. Focus on forecast reliability assessment and demand signal validation.",
                "focus": "Cyber Truck HV forecast accuracy vs EDI demand analysis",
                "purpose": "Validate and improve forecasting model performance"
            }
        }

    def load_api_key(self) -> str:
        try:
            from dotenv import load_dotenv
            load_dotenv()
            return os.getenv('OPENAI_API_KEY')
        except ImportError:
            return os.getenv('OPENAI_API_KEY')

    def load_and_preprocess_data(self, file_path: str) -> pd.DataFrame:
        logger.info(f"Loading data from {file_path}")

        df = pd.read_csv(file_path)
        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        df = df[df['role'] != 'human']

        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df

    def combine_session_data(self, session_data: pd.DataFrame) -> Dict[str, str]:
        meta_combined = []
        message_combined = []

        for _, row in session_data.iterrows():
            if pd.notna(row.get('$meta')):
                meta_combined.append(str(row['$meta']))
            if pd.notna(row.get('message')):
                message_combined.append(str(row['message']))

        return {
            'meta': ' | '.join(meta_combined),
            'message': ' | '.join(message_combined)
        }

    def process_with_flow_enhanced(self, flow_module, flow_name: str, df: pd.DataFrame, business_context: str = "") -> Dict[str, Dict[str, str]]:
        """Enhanced flow processing that passes business context to each flow"""
        logger.info(f"Processing with {flow_name} (with business context)")

        try:
            llm_client = flow_module.LLMClient(api_key=self.api_key)
            analyzer = flow_module.SessionAnalyzer(llm_client)

            # Check if the flow supports business context
            if hasattr(analyzer, 'process_all_sessions_with_context'):
                results = analyzer.process_all_sessions_with_context(df, business_context)
            else:
                # Fallback to regular processing
                results = analyzer.process_all_sessions(df)

            flow_results = {}
            for result in results:
                flow_results[result.session_id] = {
                    'title': result.title,
                    'summary': result.main_summary  # This should already be in markdown format
                }

            logger.info(f"Completed {flow_name} processing for {len(results)} sessions")
            return flow_results

        except Exception as e:
            logger.error(f"Failed to process {flow_name}: {e}")
            return {}

    def process_with_flow(self, flow_module, flow_name: str, df: pd.DataFrame) -> Dict[str, Dict[str, str]]:
        """Legacy flow processing method - kept for compatibility"""
        logger.info(f"Processing with {flow_name}")

        try:
            llm_client = flow_module.LLMClient(api_key=self.api_key)
            analyzer = flow_module.SessionAnalyzer(llm_client)

            results = analyzer.process_all_sessions(df)

            flow_results = {}
            for result in results:
                flow_results[result.session_id] = {
                    'title': result.title,
                    'summary': result.main_summary  # This should already be in markdown format
                }

            logger.info(f"Completed {flow_name} processing for {len(results)} sessions")
            return flow_results

        except Exception as e:
            logger.error(f"Failed to process {flow_name}: {e}")
            return {}

    def process_single_file(self, file_path: str) -> List[Dict[str, Any]]:
        logger.info(f"\n{'='*60}")
        logger.info(f"PROCESSING: {file_path}")
        logger.info(f"{'='*60}")

        # Get business context for this file
        test_config = self.np_test_contexts.get(file_path, {})
        business_context = test_config.get('context', '')

        logger.info(f"Business Context: {test_config.get('focus', 'General supply chain analysis')}")

        # Load data
        df = self.load_and_preprocess_data(file_path)

        # Process with all 4 flows using enhanced processing with business context
        logger.info("Processing with all 4 flows (with business context and markdown formatting)...")

        flow1_results = self.process_with_flow_enhanced(flow1, "Flow1", df, business_context)
        time.sleep(10)
        flow2_results = self.process_with_flow_enhanced(flow2, "Flow2", df, business_context)
        time.sleep(10)
        flow3_results = self.process_with_flow_enhanced(flow3, "Flow3", df, business_context)
        time.sleep(10)
        flow4_results = self.process_with_flow_enhanced(flow4, "Flow4", df, business_context)
        time.sleep(10)

        # Combine results for each session
        combined_results = []

        sessions = df.groupby('session_id')
        for session_id, session_data in sessions:
            session_id = str(session_id)
            combined_data = self.combine_session_data(session_data)

            result_row = {
                'source_file': file_path,
                'business_context': business_context,
                'session_id': session_id,
                'messages': combined_data['message'],
                'meta': combined_data['meta'],
                'flow1_title': flow1_results.get(session_id, {}).get('title', ''),
                'flow1_summary': flow1_results.get(session_id, {}).get('summary', ''),  # Markdown format preserved
                'flow2_title': flow2_results.get(session_id, {}).get('title', ''),
                'flow2_summary': flow2_results.get(session_id, {}).get('summary', ''),  # Markdown format preserved
                'flow3_title': flow3_results.get(session_id, {}).get('title', ''),
                'flow3_summary': flow3_results.get(session_id, {}).get('summary', ''),  # Markdown format preserved
                'flow4_title': flow4_results.get(session_id, {}).get('title', ''),
                'flow4_summary': flow4_results.get(session_id, {}).get('summary', '')   # Markdown format preserved
            }

            combined_results.append(result_row)

        logger.info(f"✓ Completed processing {file_path} - {len(combined_results)} sessions processed")
        return combined_results

    def process_all_np_tests(self):
        # List of NP test files to process
        np_test_files = [
            "NP_test_01.csv",
            "NP_test_02.csv",
            "NP_test_03.csv",
            "NP_test_04.csv",
            "NP_test_05.csv"
        ]

        all_results = []

        logger.info("Starting Comprehensive NP Test Files Processing...")
        logger.info(f"Files to process: {len(np_test_files)}")
        logger.info("Flows to run: Flow1, Flow2, Flow3, Flow4")
        logger.info("Output format: CSV with markdown-formatted summaries and business context")

        for file_path in np_test_files:
            if os.path.exists(file_path):
                try:
                    results = self.process_single_file(file_path)
                    all_results.extend(results)
                    logger.info(f"✓ Successfully processed {file_path}")
                except Exception as e:
                    logger.error(f"✗ Failed to process {file_path}: {str(e)}")
                    continue
            else:
                logger.warning(f"⚠ File not found: {file_path}")

        return all_results

    def save_results(self, all_results: List[Dict[str, Any]]):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"comprehensive_np_tests_results_{timestamp}.csv"

        # Create results directory if it doesn't exist
        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)

        output_path = results_dir / filename

        # Convert to DataFrame and save with proper encoding to preserve markdown
        results_df = pd.DataFrame(all_results)

        # Reorder columns for better readability
        column_order = [
            'source_file', 'business_context', 'session_id', 'messages', 'meta',
            'flow1_title', 'flow1_summary',
            'flow2_title', 'flow2_summary',
            'flow3_title', 'flow3_summary',
            'flow4_title', 'flow4_summary'
        ]

        # Ensure all columns exist
        for col in column_order:
            if col not in results_df.columns:
                results_df[col] = ''

        results_df = results_df[column_order]

        # Save with UTF-8 encoding to preserve markdown formatting
        results_df.to_csv(output_path, index=False, encoding='utf-8', quoting=1)  # quoting=1 ensures proper CSV formatting

        logger.info(f"Results saved to: {output_path}")
        logger.info(f"Total sessions processed: {len(all_results)}")
        logger.info("Markdown formatting preserved in CSV columns")

        return output_path

def main():
    processor = ComprehensiveNPTestProcessor()

    # Process all NP test files
    all_results = processor.process_all_np_tests()

    if all_results:
        output_path = processor.save_results(all_results)

        logger.info(f"\n{'='*60}")
        logger.info(f"PROCESSING COMPLETE")
        logger.info(f"{'='*60}")
        logger.info(f"Total sessions processed: {len(all_results)}")
        logger.info(f"Results saved to: {output_path}")
        logger.info("Format: CSV with markdown-formatted summaries")

        # Display summary
        df = pd.DataFrame(all_results)
        logger.info(f"\nSummary by file:")
        for file_name in df['source_file'].unique():
            count = len(df[df['source_file'] == file_name])
            logger.info(f"  {file_name}: {count} sessions")

        logger.info(f"\nOutput columns:")
        logger.info("- source_file: NP test file name")
        logger.info("- business_context: Business context paragraph for the file")
        logger.info("- session_id: Unique session identifier")
        logger.info("- messages: Combined session messages")
        logger.info("- meta: Combined session metadata")
        logger.info("- flow1_title, flow1_summary: Flow 1 results (markdown format)")
        logger.info("- flow2_title, flow2_summary: Flow 2 results (markdown format)")
        logger.info("- flow3_title, flow3_summary: Flow 3 results (markdown format)")
        logger.info("- flow4_title, flow4_summary: Flow 4 results (markdown format)")

    else:
        logger.error("No results generated!")

if __name__ == "__main__":
    main()
