import pandas as pd
import json
import time
import logging
from typing import List, Dict, Any
import sys
import os
from datetime import datetime
from pathlib import Path
import openai
from dataclasses import dataclass
from typing import Tuple

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SessionSummary:
    session_id: str
    individual_summaries: List[str]
    main_summary: str
    title: str
    message_count: int
    processing_time: float
    total_input_tokens: int
    total_output_tokens: int
    individual_input_tokens: List[int]
    individual_output_tokens: List[int]
    session_summary_input_tokens: int
    session_summary_output_tokens: int
    title_input_tokens: int
    title_output_tokens: int

class LLMClient:
    def __init__(self, api_key: str, model_name: str = "gpt-4o-mini"):
        self.api_key = api_key
        self.model_name = model_name
        self.client = openai.OpenAI(api_key=api_key)
    
    def count_tokens(self, prompt: str) -> int:
        return len(prompt.split()) * 1.3  # Rough estimate
    
    def generate_content(self, prompt: str, max_retries: int = 3) -> Tuple[str, int, int]:
        for attempt in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=200,  # Reduced for shorter summaries
                    temperature=0.7
                )
                
                content = response.choices[0].message.content.strip()
                input_tokens = response.usage.prompt_tokens
                output_tokens = response.usage.completion_tokens
                
                return content, input_tokens, output_tokens
                
            except Exception as e:
                logger.warning(f"API call failed (attempt {attempt + 1}): {str(e)}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2 ** attempt)

class ImprovedPromptEngine:
    @staticmethod
    def create_individual_summary_prompt(summaries: List[str], entities_list: List[str], reasoning_list: List[str], business_context: str = "") -> str:
        summaries_text = "\n\n".join([f"ANALYSIS {i+1}: {summary}" for i, summary in enumerate(summaries)])
        entities_text = "\n".join([f"ENTITIES {i+1}: {entities}" for i, entities in enumerate(entities_list) if entities])
        reasoning_text = "\n\n".join([f"REASONING {i+1}: {reasoning}" for i, reasoning in enumerate(reasoning_list) if reasoning])
        
        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""
        
        return f"""
You are a Yazaki supply chain analyst specializing in automotive component analysis and demand forecasting. Analyze the conversation and provide a concise markdown summary.

YAZAKI-SPECIFIC ANALYSIS REQUIREMENTS:
- Focus on automotive component demand patterns, supply chain performance, and fulfillment metrics
- Use Yazaki terminology: components, harnesses, connectors, assemblies, production lines
- Emphasize customer relationships (Tesla, Rivian, etc.) and part numbers when available
- Address supply chain visibility, demand signals, and production planning specifics

OUTPUT FORMAT - MARKDOWN WITH BULLET POINTS:
• **Key Finding**: [Main insight about component demand/supply]
• **Impact**: [Effect on production planning or customer fulfillment]  
• **Action Required**: [Specific next steps for supply chain team]

CRITICAL GUIDELINES:
- Maximum 3 bullet points, 15 words per bullet point
- If insufficient context exists, state: "• **Insufficient Data**: Cannot generate meaningful analysis without additional context"
- Focus on actionable supply chain insights
- Use specific part numbers, customer names, and quantities when available
- Avoid generic supply chain jargon

CONVERSATION DATA:
{summaries_text}

{entities_text if entities_text else ""}

{reasoning_text if reasoning_text else ""}
{context_section}

Provide only the markdown summary with bullet points as specified above.
"""

    @staticmethod
    def create_session_summary_prompt(individual_summaries: List[str], session_id: str, business_context: str = "") -> str:
        summaries_text = "\n\n".join([f"SUMMARY {i+1}:\n{summary}" for i, summary in enumerate(individual_summaries)])
        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""
        
        return f"""
You are a Yazaki supply chain director synthesizing conversation analysis for executive reporting.

EXECUTIVE SYNTHESIS REQUIREMENTS:
- Consolidate individual summaries into cohesive supply chain insights
- Focus on Yazaki's automotive component business impact
- Highlight customer-specific issues (Tesla, Rivian, etc.) and part number analysis
- Address demand forecasting, production planning, and fulfillment performance

OUTPUT FORMAT - MARKDOWN EXECUTIVE SUMMARY:
## Supply Chain Analysis Summary

• **Primary Issue**: [Main supply chain challenge or opportunity]
• **Customer Impact**: [Specific effect on key automotive customers]
• **Recommended Action**: [Executive decision required for supply chain optimization]

CRITICAL REQUIREMENTS:
- Maximum 3 bullet points, 20 words per bullet point
- If summaries indicate insufficient data, state: "• **Data Limitation**: Analysis incomplete due to insufficient context in source conversations"
- Focus on executive-level supply chain decisions
- Include specific customer names, part numbers, and metrics when available
- Emphasize Yazaki's competitive position and operational performance

INDIVIDUAL SUMMARIES:
{summaries_text}
{context_section}

Provide only the markdown executive summary as specified above.
"""

    @staticmethod
    def create_title_generation_prompt(main_summary: str, session_id: str, business_context: str = "") -> str:
        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""
        
        return f"""
Generate a concise title for this Yazaki supply chain analysis session.

TITLE REQUIREMENTS:
- 3-6 words maximum
- Include customer name (Tesla, Rivian, etc.) when relevant
- Focus on supply chain action: Analysis, Forecast, Optimization, Review
- Use Yazaki terminology: Components, Demand, Fulfillment, Supply

EXAMPLES:
- "Tesla Component Demand Analysis"
- "Rivian Supply Chain Review" 
- "Component Forecast Optimization"
- "Customer Fulfillment Assessment"

SUMMARY TO ANALYZE:
{main_summary}
{context_section}

Provide only the title in plain text without quotes or formatting.
"""

class NPTestProcessor:
    def __init__(self):
        self.api_key = self.load_api_key()
        
        if not self.api_key:
            raise ValueError("No API key found in .env file")
            
        logger.info("API key loaded successfully")
        
        # Define business context for each NP test file
        self.np_test_contexts = {
            "NP_test_01.csv": {
                "context": "Tesla demand and fulfillment trends analysis across 2023 and 2024. Focus on year-over-year order volume dynamics, total shipment activity, and anomalies between forecasted and actual orders for forecast alignment with Tesla planners.",
                "focus": "Tesla demand forecasting and shipment analysis",
                "purpose": "Support forecast alignment call with Tesla planning team"
            },
            "NP_test_02.csv": {
                "context": "Rivian R1S vehicle line supply performance and demand concentration assessment. Focus on identifying high-volume parts, sustained demand components, and fulfillment effectiveness analysis including detailed review of part PT00876922-E.",
                "focus": "Rivian R1S supply performance and key component analysis", 
                "purpose": "Optimize supply chain for high-volume Rivian components"
            },
            "NP_test_03.csv": {
                "context": "Model 3 H LV carline demand landscape comprehensive analysis. Focus on historical customer ordering patterns, recent Tesla news impact, and external factors influencing demand or production planning.",
                "focus": "Model 3 H LV demand patterns and market influence analysis",
                "purpose": "Enhance demand planning with market intelligence integration"
            },
            "NP_test_04.csv": {
                "context": "Tesla recent order-to-fulfillment performance evaluation. Focus on demand conversion to shipments over time, identifying fulfillment inefficiencies or improvements in the supply chain process.",
                "focus": "Tesla order fulfillment performance optimization",
                "purpose": "Improve demand-to-shipment conversion rates"
            },
            "NP_test_05.csv": {
                "context": "SupplyWhy crystal ball projection forecast accuracy evaluation versus EDI demand signals for Cyber Truck HV carline. Focus on forecast reliability assessment and demand signal validation.",
                "focus": "Cyber Truck HV forecast accuracy vs EDI demand analysis",
                "purpose": "Validate and improve forecasting model performance"
            }
        }
        
        self.llm_client = LLMClient(self.api_key)
        self.prompt_engine = ImprovedPromptEngine()
        
    def load_api_key(self) -> str:
        try:
            from dotenv import load_dotenv
            load_dotenv()
            return os.getenv('OPENAI_API_KEY')
        except ImportError:
            return os.getenv('OPENAI_API_KEY')
    
    def load_and_preprocess_data(self, file_path: str) -> pd.DataFrame:
        logger.info(f"Loading data from {file_path}")
        
        df = pd.read_csv(file_path)
        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        df = df[df['role'] != 'human']
        
        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df
        
    def extract_message_fields(self, message_string: str) -> Dict[str, str]:
        try:
            if pd.isna(message_string) or message_string == '':
                return {"summary": "No summary available", "reasoning": "No reasoning available"}
            
            try:
                message_data = json.loads(message_string)
                summary = message_data.get('summary', message_data.get('output', ''))
                reasoning = message_data.get('reasoning', '')
                entities = message_data.get('entities', '')
                
                return {
                    "summary": summary if summary else "No summary available",
                    "reasoning": reasoning if reasoning else "No reasoning available",
                    "entities": str(entities) if entities else ""
                }
            except json.JSONDecodeError:
                return {
                    "summary": str(message_string)[:200] + "..." if len(str(message_string)) > 200 else str(message_string),
                    "reasoning": "JSON parsing failed",
                    "entities": ""
                }
        except Exception as e:
            return {"summary": f"Error extracting fields: {str(e)}", "reasoning": "Error", "entities": ""}

    def extract_entities(self, meta_string: str) -> str:
        try:
            if pd.isna(meta_string) or meta_string == '':
                return ""
            meta_data = json.loads(meta_string)
            entities = meta_data.get('entities', {})
            if isinstance(entities, dict):
                return "; ".join([f"{k}: {', '.join(v) if isinstance(v, list) else v}" for k, v in entities.items()])
            return str(entities)
        except:
            return ""

    def generate_individual_summary(self, row: pd.Series, business_context: str) -> Tuple[str, int, int]:
        message_fields = self.extract_message_fields(row['message'])
        entities = self.extract_entities(row.get('$meta', ''))
        
        prompt = self.prompt_engine.create_individual_summary_prompt(
            [message_fields['summary']], 
            [entities], 
            [message_fields['reasoning']],
            business_context
        )
        
        return self.llm_client.generate_content(prompt)

    def generate_session_summary(self, individual_summaries: List[str], session_id: str, business_context: str) -> Tuple[str, int, int]:
        prompt = self.prompt_engine.create_session_summary_prompt(individual_summaries, session_id, business_context)
        return self.llm_client.generate_content(prompt)

    def generate_session_title(self, main_summary: str, session_id: str, business_context: str) -> Tuple[str, int, int]:
        prompt = self.prompt_engine.create_title_generation_prompt(main_summary, session_id, business_context)
        return self.llm_client.generate_content(prompt)

    def analyze_session(self, session_data: pd.DataFrame, session_id: str, business_context: str) -> SessionSummary:
        start_time = time.time()
        individual_summaries = []
        individual_input_tokens = []
        individual_output_tokens = []
        
        for _, row in session_data.iterrows():
            try:
                summary, input_tokens, output_tokens = self.generate_individual_summary(row, business_context)
                individual_summaries.append(summary)
                individual_input_tokens.append(input_tokens)
                individual_output_tokens.append(output_tokens)
            except Exception as e:
                logger.error(f"Error processing message in session {session_id}: {str(e)}")
                individual_summaries.append("• **Processing Error**: Unable to analyze this conversation segment")
                individual_input_tokens.append(0)
                individual_output_tokens.append(0)
        
        try:
            main_summary, session_input_tokens, session_output_tokens = self.generate_session_summary(
                individual_summaries, session_id, business_context)
        except Exception as e:
            logger.error(f"Error generating session summary for {session_id}: {str(e)}")
            main_summary = "• **Session Analysis Error**: Unable to generate comprehensive summary"
            session_input_tokens = session_output_tokens = 0
        
        try:
            title, title_input_tokens, title_output_tokens = self.generate_session_title(
                main_summary, session_id, business_context)
        except Exception as e:
            logger.error(f"Error generating title for {session_id}: {str(e)}")
            title = "Supply Chain Analysis"
            title_input_tokens = title_output_tokens = 0
        
        processing_time = time.time() - start_time
        
        return SessionSummary(
            session_id=session_id,
            individual_summaries=individual_summaries,
            main_summary=main_summary,
            title=title,
            message_count=len(session_data),
            processing_time=processing_time,
            total_input_tokens=sum(individual_input_tokens) + session_input_tokens + title_input_tokens,
            total_output_tokens=sum(individual_output_tokens) + session_output_tokens + title_output_tokens,
            individual_input_tokens=individual_input_tokens,
            individual_output_tokens=individual_output_tokens,
            session_summary_input_tokens=session_input_tokens,
            session_summary_output_tokens=session_output_tokens,
            title_input_tokens=title_input_tokens,
            title_output_tokens=title_output_tokens
        )

    def process_all_sessions(self, df: pd.DataFrame, business_context: str) -> List[SessionSummary]:
        unique_sessions = df['session_id'].unique()
        session_summaries = []
        
        logger.info(f"Processing {len(unique_sessions)} sessions...")
        
        for i, session_id in enumerate(unique_sessions, 1):
            logger.info(f"Processing session {i}/{len(unique_sessions)}: {session_id}")
            session_data = df[df['session_id'] == session_id].copy()
            
            try:
                session_summary = self.analyze_session(session_data, session_id, business_context)
                session_summaries.append(session_summary)
                logger.info(f"✓ Completed session {session_id}")
            except Exception as e:
                logger.error(f"✗ Failed to process session {session_id}: {str(e)}")
                continue
        
        return session_summaries

    def save_results(self, session_summaries: List[SessionSummary], file_path: str):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"results_{timestamp}.csv"
        output_path = os.path.join("results", filename)
        
        os.makedirs("results", exist_ok=True)
        
        results_data = []
        for summary in session_summaries:
            results_data.append({
                'session_id': summary.session_id,
                'title': summary.title,
                'main_summary': summary.main_summary,
                'individual_summaries': ' | '.join(summary.individual_summaries),
                'message_count': summary.message_count,
                'processing_time': summary.processing_time,
                'total_input_tokens': summary.total_input_tokens,
                'total_output_tokens': summary.total_output_tokens,
                'source_file': file_path
            })
        
        results_df = pd.DataFrame(results_data)
        results_df.to_csv(output_path, index=False)
        logger.info(f"Results saved to {output_path}")
        return output_path

    def process_np_test_file(self, file_path: str):
        logger.info(f"\n{'='*50}")
        logger.info(f"PROCESSING: {file_path}")
        logger.info(f"{'='*50}")
        
        # Get business context for this file
        test_config = self.np_test_contexts.get(file_path, {})
        business_context = test_config.get('context', '')
        
        logger.info(f"Business Context: {test_config.get('focus', 'General supply chain analysis')}")
        
        # Load and process data
        df = self.load_and_preprocess_data(file_path)
        
        # Process all sessions
        session_summaries = self.process_all_sessions(df, business_context)
        
        # Save results
        output_path = self.save_results(session_summaries, file_path)
        
        logger.info(f"✓ Completed processing {file_path}")
        logger.info(f"✓ Results saved to {output_path}")
        
        return session_summaries

def main():
    processor = NPTestProcessor()
    
    # List of NP test files to process
    np_test_files = [
        "NP_test_01.csv",
        "NP_test_02.csv", 
        "NP_test_03.csv",
        "NP_test_04.csv",
        "NP_test_05.csv"
    ]
    
    all_results = []
    
    logger.info("Starting NP Test Files Processing...")
    logger.info(f"Files to process: {len(np_test_files)}")
    
    for file_path in np_test_files:
        if os.path.exists(file_path):
            try:
                results = processor.process_np_test_file(file_path)
                all_results.extend(results)
                logger.info(f"✓ Successfully processed {file_path}")
            except Exception as e:
                logger.error(f"✗ Failed to process {file_path}: {str(e)}")
                continue
        else:
            logger.warning(f"⚠ File not found: {file_path}")
    
    logger.info(f"\n{'='*50}")
    logger.info(f"PROCESSING COMPLETE")
    logger.info(f"{'='*50}")
    logger.info(f"Total sessions processed: {len(all_results)}")
    logger.info(f"Results saved in: ./results/")

if __name__ == "__main__":
    main() 