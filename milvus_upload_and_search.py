import pandas as pd
import ast
from pymilvus import connections, utility, FieldSchema, CollectionSchema, DataType, Collection

# --- Milvus credentials from file ---
def read_milvus_credentials(cred_path='zilliz-cloud-Free-01-username-password.txt'):
    with open(cred_path, 'r') as f:
        lines = f.readlines()
        user = lines[0].split(':', 1)[1].strip()
        password = lines[1].split(':', 1)[1].strip()
    # Use the provided Milvus Cloud URI
    uri = 'https://in03-1a4f34918e6715b.serverless.gcp-us-west1.cloud.zilliz.com'
    return uri, user, password

# --- Upload embeddings from CSV to Milvus ---
def upload_embeddings(csv_path, collection_name, uri, user, password):
    # Read CSV
    df = pd.read_csv(csv_path)
    # Parse embeddings
    df['embedding'] = df['embedding'].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)
    dim = len(df['embedding'].iloc[0])
    # Connect to Milvus
    connections.connect("default", uri=uri, user=user, password=password, secure=True)
    # Create collection if not exists
    if not utility.has_collection(collection_name):
        fields = [
            FieldSchema(name="id", dtype=DataType.VARCHAR, is_primary=True, auto_id=False, max_length=64),
            FieldSchema(name="embedding", dtype=DataType.FLOAT_VECTOR, dim=dim)
        ]
        schema = CollectionSchema(fields, description="Embeddings collection")
        collection = Collection(collection_name, schema, consistency_level="Strong")
    else:
        collection = Collection(collection_name)
    # Prepare data for insertion
    data = [df['id'].astype(str).tolist(), df['embedding'].tolist()]
    collection.insert(data)
    print(f"Uploaded {len(df)} embeddings to collection '{collection_name}'")
    # --- Create index on embedding field (required for search) ---
    index_params = {
        "index_type": "IVF_FLAT",
        "metric_type": "L2",
        "params": {"nlist": 128}
    }
    collection.create_index(field_name="embedding", index_params=index_params)
    print("Index created on 'embedding' field.")
    return collection, dim

# --- Perform similarity search ---
def search_embedding(collection, query_vector, top_k=3):
    # Load collection into memory
    collection.load()
    search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
    results = collection.search(
        [query_vector],
        "embedding",
        search_params,
        limit=top_k,
        output_fields=["id"]
    )
    print(f"Top {top_k} most similar IDs:")
    for hit in results[0]:
        print(f"ID: {hit.id}, Distance: {hit.distance}")

# --- Example usage ---
if __name__ == "__main__":
    # Use the provided CSV file and collection name
    csv_path = "sw_stm.query-19.csv"  # Provided CSV file
    collection_name = "my_collection"  # You can change this if needed
    uri, user, password = read_milvus_credentials()

    # Upload embeddings
    collection, dim = upload_embeddings(csv_path, collection_name, uri, user, password)

    # Example: search using the first embedding in the CSV
    df = pd.read_csv(csv_path)
    query_vector = ast.literal_eval(df['embedding'].iloc[0])
    search_embedding(collection, query_vector, top_k=3)

# ---
# Notes:
# - The script now uses the provided Milvus Cloud URI and CSV file.
# - The script assumes the CSV has columns: 'id' and 'embedding' (embedding as a stringified list).
# - This script only uses the credentials text file, not environment variables.
# - Uses FieldSchema and CollectionSchema as required by pymilvus ORM API.
# - Now creates an IVF_FLAT index on the embedding field before search. 