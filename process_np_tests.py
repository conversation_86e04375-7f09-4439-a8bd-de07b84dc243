import pandas as pd
import json
import time
import logging
from typing import List, Dict, Any
import sys
import os
from datetime import datetime
from pathlib import Path

import flow1
import flow2
import flow3
import flow4

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NPTestProcessor:
    def __init__(self):
        self.api_key = self.load_api_key()
        
        if not self.api_key:
            raise ValueError("No API key found in .env file")
            
        logger.info("API key loaded successfully")
        
        # Define business context for each NP test file
        self.np_test_contexts = {
            "NP_test_01.csv": {
                "context": "To evaluate Tesla's demand and fulfillment trends across 2023 and 2024. The aim is to understand year-over-year order volume dynamics, total shipment activity, and anomalies between forecasted and actual orders ahead of the forecast alignment call with Tesla planners",
                "focus": "Tesla demand/fulfillment trends, order volume dynamics, shipment analysis",
                "business_purpose": "Forecast alignment preparation for Tesla planners"
            },
            "NP_test_02.csv": {
                "context": "To assess supply performance and demand concentration for Rivian's R1S vehicle line, focusing on identifying high-volume parts. The goal is to pinpoint key components with sustained demand and understand how effectively they were fulfilled, including a deeper look at part PT00876922-E",
                "focus": "Rivian R1S supply performance, high-volume parts analysis, component fulfillment effectiveness",
                "business_purpose": "Supply chain optimization for Rivian R1S vehicle line"
            },
            "NP_test_03.csv": {
                "context": "To gain a comprehensive view of the Model 3 H LV carline's demand landscape, review historical customer ordering patterns, and supplement the analysis with recent Tesla news to account for any external factors that may influence demand or production planning",
                "focus": "Model 3 H LV demand patterns, customer ordering behavior, external market factors",
                "business_purpose": "Comprehensive demand planning for Model 3 H LV carline"
            },
            "NP_test_04.csv": {
                "context": "To understand Tesla's recent order-to-fulfillment performance. The objective is to evaluate how well demand is converting into shipments over time and identify any emerging fulfillment inefficiencies or improvements",
                "focus": "Tesla order-to-fulfillment conversion, shipment efficiency analysis, performance trends",
                "business_purpose": "Yazaki fulfillment process optimization for Tesla orders"
            },
            "NP_test_05.csv": {
                "context": "To evaluate the forecast accuracy and reliability of SupplyWhy's crystal ball projections compared to EDI demand signals for the Cyber Truck HV carline",
                "focus": "Cyber Truck HV forecast accuracy, SupplyWhy crystal ball vs EDI comparison, demand prediction reliability",
                "business_purpose": "Forecast methodology validation for Cyber Truck HV production planning"
            }
        }
        
    def load_api_key(self) -> str:
        from dotenv import load_dotenv
        load_dotenv()
        return os.getenv('OPENAI_API_KEY')
    
    def load_and_preprocess_data_from_file(self, file_path: str) -> pd.DataFrame:
        logger.info(f"Loading data from {file_path}")
        
        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            df = pd.read_excel(file_path)
        else:
            df = pd.read_csv(file_path)
            
        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        df = df[df['role'] != 'human']
        
        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df
    
    def process_with_flow(self, flow_module, flow_name: str, df: pd.DataFrame, business_context: Dict[str, str]) -> Dict[str, Dict[str, str]]:
        logger.info(f"Processing with {flow_name}")
        
        try:
            llm_client = flow_module.LLMClient(api_key=self.api_key)
            analyzer = flow_module.SessionAnalyzer(llm_client)
            
            # Add business context to the analyzer
            analyzer.business_context = business_context
            
            results = analyzer.process_all_sessions(df)
            
            flow_results = {}
            for result in results:
                flow_results[result.session_id] = {
                    'title': result.title,
                    'summary': result.main_summary
                }
            
            logger.info(f"Completed {flow_name} processing for {len(results)} sessions")
            return flow_results
            
        except Exception as e:
            logger.error(f"Failed to process {flow_name}: {e}")
            return {}
    
    def process_single_np_test(self, file_name: str) -> Dict[str, Any]:
        """Process a single NP test file with its specific business context"""
        
        if file_name not in self.np_test_contexts:
            raise ValueError(f"No context defined for {file_name}")
        
        business_context = self.np_test_contexts[file_name]
        logger.info(f"Processing {file_name} with context: {business_context['context']}")
        
        # Load the data
        df = self.load_and_preprocess_data_from_file(file_name)
        
        # Process with all flows
        flow1_results = self.process_with_flow(flow1, "Flow1", df, business_context)
        time.sleep(10)
        flow2_results = self.process_with_flow(flow2, "Flow2", df, business_context)
        time.sleep(10)
        flow3_results = self.process_with_flow(flow3, "Flow3", df, business_context)
        time.sleep(10)
        flow4_results = self.process_with_flow(flow4, "Flow4", df, business_context)
        time.sleep(10)
        
        combined_results = []
        
        sessions = df.groupby('session_id')
        for session_id, session_data in sessions:
            session_id = str(session_id)
            
            # Combine session data
            meta_combined = []
            message_combined = []
            
            for _, row in session_data.iterrows():
                if pd.notna(row.get('$meta')):
                    meta_combined.append(str(row['$meta']))
                if pd.notna(row.get('message')):
                    message_combined.append(str(row['message']))
            
            result_row = {
                'session_id': session_id,
                'meta': ' | '.join(meta_combined),
                'message': ' | '.join(message_combined),
                'business_context': business_context['context'],
                'business_focus': business_context['focus'],
                'business_purpose': business_context['business_purpose'],
                'flow1_title': flow1_results.get(session_id, {}).get('title', ''),
                'flow1_summary': flow1_results.get(session_id, {}).get('summary', ''),
                'flow2_title': flow2_results.get(session_id, {}).get('title', ''),
                'flow2_summary': flow2_results.get(session_id, {}).get('summary', ''),
                'flow3_title': flow3_results.get(session_id, {}).get('title', ''),
                'flow3_summary': flow3_results.get(session_id, {}).get('summary', ''),
                'flow4_title': flow4_results.get(session_id, {}).get('title', ''),
                'flow4_summary': flow4_results.get(session_id, {}).get('summary', '')
            }
            
            combined_results.append(result_row)
        
        return {
            'status': 'success',
            'file_name': file_name,
            'business_context': business_context,
            'total_sessions': len(combined_results),
            'processing_time': datetime.now().isoformat(),
            'results': combined_results
        }
    
    def process_all_np_tests(self) -> Dict[str, Any]:
        """Process all NP test files sequentially"""
        
        all_results = {}
        
        for file_name in ["NP_test_01.csv", "NP_test_02.csv", "NP_test_03.csv", "NP_test_04.csv", "NP_test_05.csv"]:
            try:
                logger.info(f"Starting processing of {file_name}")
                result = self.process_single_np_test(file_name)
                all_results[file_name] = result
                
                # Save individual results
                self.save_results_to_csv(result, file_name)
                
                logger.info(f"Completed processing {file_name}")
                
            except Exception as e:
                logger.error(f"Failed to process {file_name}: {e}")
                all_results[file_name] = {
                    'status': 'error',
                    'error': str(e),
                    'file_name': file_name
                }
        
        return all_results
    
    def save_results_to_csv(self, data: Dict[str, Any], file_name: str) -> None:
        """Save results to CSV in the results folder"""
        
        results = data.get('results', [])
        
        if not results:
            logger.warning(f"No results found for {file_name}")
            return
            
        logger.info(f"Saving {len(results)} records for {file_name}...")
        
        csv_rows = []
        
        for result in results:
            row = {
                'session_id': result.get('session_id', ''),
                'business_context': result.get('business_context', ''),
                'business_focus': result.get('business_focus', ''),
                'business_purpose': result.get('business_purpose', ''),
                'flow1_title': result.get('flow1_title', ''),
                'flow1_summary': result.get('flow1_summary', ''),
                'flow2_title': result.get('flow2_title', ''),
                'flow2_summary': result.get('flow2_summary', ''),
                'flow3_title': result.get('flow3_title', ''),
                'flow3_summary': result.get('flow3_summary', ''),
                'flow4_title': result.get('flow4_title', ''),
                'flow4_summary': result.get('flow4_summary', ''),
                'message': result.get('message', ''),
                'meta': result.get('meta', '')
            }
            csv_rows.append(row)
        
        df = pd.DataFrame(csv_rows)
        
        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        base_name = file_name.replace('.csv', '')
        output_csv_path = results_dir / f"{base_name}_results_{timestamp}.csv"
        
        df.to_csv(output_csv_path, index=False, encoding='utf-8')
        
        logger.info(f"CSV file saved to: {output_csv_path}")

def main():
    """Main function to process all NP test files"""
    processor = NPTestProcessor()
    
    logger.info("Starting NP Test processing with improved summary generation...")
    
    # Process all NP test files
    all_results = processor.process_all_np_tests()
    
    # Log summary
    for file_name, result in all_results.items():
        if result.get('status') == 'success':
            logger.info(f"{file_name}: Processed {result.get('total_sessions', 0)} sessions successfully")
        else:
            logger.error(f"{file_name}: Processing failed - {result.get('error', 'Unknown error')}")
    
    logger.info("NP Test processing completed!")

if __name__ == "__main__":
    main() 