#!/usr/bin/env python3

import os
import pandas as pd
import logging
import asyncio
import concurrent.futures
from datetime import datetime
from typing import Dict, List, Tuple
from dotenv import load_dotenv

# Import all flow modules
from flow1 import SessionAnalyzer as Flow1Analyzer, LLMClient
from flow2 import SessionAnalyzer as Flow2Analyzer
from flow3 import SessionAnalyzer as Flow3Analyzer
from flow4 import SessionAnalyzer as Flow4Analyzer

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ParallelNPProcessor:
    def __init__(self):
        load_dotenv()
        self.api_key = os.getenv('OPENAI_API_KEY')
        if not self.api_key:
            raise ValueError("OPENAI_API_KEY not found in environment variables")

        # Business contexts for each file
        self.business_contexts = {
            "NP_test_01.csv": "Tesla demand forecasting and shipment analysis",
            "NP_test_02.csv": "Rivian R1S supply performance and key component analysis",
            "NP_test_03.csv": "Model 3 H LV demand patterns and market influence analysis",
            "NP_test_04.csv": "Tesla order fulfillment performance optimization",
            "NP_test_05.csv": "Cyber Truck HV forecast accuracy vs EDI demand analysis"
        }

        # Initialize LLM client
        self.llm_client = LLMClient(self.api_key)

    def process_single_file_flow(self, file_path: str, flow_name: str, flow_analyzer_class) -> Dict:
        """Process a single file with a single flow"""
        try:
            logger.info(f"Starting {flow_name} processing for {file_path}")

            # Initialize analyzer
            analyzer = flow_analyzer_class(self.llm_client)

            # Load and process data
            df = analyzer.load_and_preprocess_data(file_path)

            # Get business context
            business_context = self.business_contexts.get(os.path.basename(file_path), "")

            # Process sessions
            if hasattr(analyzer, 'process_all_sessions_with_context'):
                # For flows that support business context (Flow2)
                session_results = analyzer.process_all_sessions_with_context(df, business_context)
            else:
                # For flows that don't have context support yet (Flow1, Flow3, Flow4)
                session_results = analyzer.process_all_sessions(df)

            results = []
            for session_result in session_results:
                session_data = df[df['session_id'] == session_result.session_id]
                results.append({
                    'session_id': session_result.session_id,
                    'title': session_result.title,
                    'summary': session_result.main_summary,
                    'messages': self.combine_messages(session_data),
                    'meta': self.combine_meta(session_data)
                })

            logger.info(f"Completed {flow_name} processing for {file_path}")
            return {
                'file': file_path,
                'flow': flow_name,
                'results': results,
                'success': True
            }

        except Exception as e:
            logger.error(f"Error in {flow_name} processing for {file_path}: {e}")
            return {
                'file': file_path,
                'flow': flow_name,
                'results': [],
                'success': False,
                'error': str(e)
            }

    def combine_messages(self, session_data: pd.DataFrame) -> str:
        """Combine all messages for a session"""
        messages = []
        for _, row in session_data.iterrows():
            if pd.notna(row.get('message')):
                messages.append(str(row['message']))
        return ' | '.join(messages)

    def combine_meta(self, session_data: pd.DataFrame) -> str:
        """Combine all meta information for a session"""
        meta_list = []
        for _, row in session_data.iterrows():
            if pd.notna(row.get('$meta')):
                meta_list.append(str(row['$meta']))
        return ' | '.join(meta_list)

    async def process_all_files_parallel(self) -> pd.DataFrame:
        """Process all NP test files with all flows in parallel"""
        logger.info("Starting parallel processing of all NP test files")

        # Get all NP test files
        np_files = [f for f in os.listdir('.') if f.startswith('NP_test_') and f.endswith('.csv')]
        logger.info(f"Found {len(np_files)} NP test files: {np_files}")

        # Define flow configurations
        flows = [
            ('Flow1', Flow1Analyzer),
            ('Flow2', Flow2Analyzer),
            ('Flow3', Flow3Analyzer),
            ('Flow4', Flow4Analyzer)
        ]

        # Create all tasks for parallel execution
        tasks = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=20) as executor:
            for file_path in np_files:
                for flow_name, flow_class in flows:
                    task = executor.submit(
                        self.process_single_file_flow,
                        file_path,
                        flow_name,
                        flow_class
                    )
                    tasks.append(task)

            # Wait for all tasks to complete
            logger.info(f"Executing {len(tasks)} parallel tasks...")
            results = []
            for future in concurrent.futures.as_completed(tasks):
                result = future.result()
                results.append(result)
                if result['success']:
                    logger.info(f"✓ Completed {result['flow']} for {result['file']}")
                else:
                    logger.error(f"✗ Failed {result['flow']} for {result['file']}: {result.get('error', 'Unknown error')}")

        # Merge results into final DataFrame
        return self.merge_results(results)

    def merge_results(self, results: List[Dict]) -> pd.DataFrame:
        """Merge all results into a single DataFrame"""
        logger.info("Merging all results into final DataFrame")

        # Group results by file and session
        file_session_data = {}

        for result in results:
            if not result['success']:
                continue

            file_name = os.path.basename(result['file'])

            for session_result in result['results']:
                session_id = session_result['session_id']
                key = (file_name, session_id)

                if key not in file_session_data:
                    file_session_data[key] = {
                        'source_file': file_name,
                        'business_context': self.business_contexts.get(file_name, ""),
                        'session_id': session_id,
                        'messages': session_result['messages'],
                        'meta': session_result['meta']
                    }

                # Add flow-specific results
                flow_name = result['flow'].lower()
                file_session_data[key][f'{flow_name}_title'] = session_result['title']
                file_session_data[key][f'{flow_name}_summary'] = session_result['summary']

        # Convert to DataFrame
        final_data = list(file_session_data.values())
        df = pd.DataFrame(final_data)

        # Ensure all flow columns exist
        for flow in ['flow1', 'flow2', 'flow3', 'flow4']:
            if f'{flow}_title' not in df.columns:
                df[f'{flow}_title'] = ""
            if f'{flow}_summary' not in df.columns:
                df[f'{flow}_summary'] = ""

        # Reorder columns
        column_order = [
            'source_file', 'business_context', 'session_id', 'messages', 'meta',
            'flow1_title', 'flow1_summary', 'flow2_title', 'flow2_summary',
            'flow3_title', 'flow3_summary', 'flow4_title', 'flow4_summary'
        ]

        df = df.reindex(columns=column_order, fill_value="")

        logger.info(f"Final DataFrame created with {len(df)} rows and {len(df.columns)} columns")
        return df

async def main():
    processor = ParallelNPProcessor()

    # Process all files in parallel
    start_time = datetime.now()
    results_df = await processor.process_all_files_parallel()
    end_time = datetime.now()

    # Save results
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_file = f"results/parallel_np_results_quantitative_{timestamp}.csv"
    os.makedirs("results", exist_ok=True)
    results_df.to_csv(output_file, index=False)

    processing_time = (end_time - start_time).total_seconds()

    logger.info("="*80)
    logger.info("PARALLEL PROCESSING COMPLETE")
    logger.info("="*80)
    logger.info(f"Total processing time: {processing_time:.2f} seconds")
    logger.info(f"Total sessions processed: {len(results_df)}")
    logger.info(f"Results saved to: {output_file}")
    logger.info(f"Format: CSV with quantitative markdown-formatted summaries")
    logger.info("="*80)

if __name__ == "__main__":
    asyncio.run(main())
