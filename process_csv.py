import pandas as pd
import os
from datetime import datetime
from pathlib import Path
import logging
from pymilvus import MilvusClient
import ast

# Import the flow modules
import flow1
import flow2
import flow3
import flow4
from dotenv import load_dotenv

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)
load_dotenv()

def read_milvus_credentials():
    # Try environment variables first
    uri = os.getenv('MILVUS_URI')
    user = os.getenv('MILVUS_USER')
    password = os.getenv('MILVUS_PASSWORD')
    if uri and user and password:
        return uri, user, password
    # Fallback to file
    cred_path = 'zilliz-cloud-Free-01-username-password.txt'
    if os.path.exists(cred_path):
        with open(cred_path, 'r') as f:
            lines = f.readlines()
            user = lines[0].split(':', 1)[1].strip()
            password = lines[1].split(':', 1)[1].strip()
        uri = os.getenv('MILVUS_URI')
        if not uri:
            raise ValueError('Set MILVUS_URI env variable to your Milvus Cloud endpoint')
        return uri, user, password
    raise ValueError('Milvus credentials not found. Set env vars or provide zilliz-cloud-Free-01-username-password.txt')

class CSVProcessor:
    def __init__(self):
        self.api_key = self.load_api_key()
        
        if not self.api_key:
            raise ValueError("No API key found in .env file")
            
        logger.info("API key loaded successfully")
    
    def load_api_key(self) -> str:
        from dotenv import load_dotenv
        load_dotenv(override=True)
        return os.getenv('OPENAI_API_KEY')
    
    def load_and_preprocess_data(self, file_path: str) -> pd.DataFrame:
        """Load and preprocess the CSV file"""
        logger.info(f"Loading data from {file_path}")
        
        # Read the CSV file
        df = pd.read_csv(file_path)
        
        # Clean the data
        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        
        # Filter out human messages (keep only assistant messages)
        df = df[df['role'] != 'human']
        
        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df
    
    def combine_session_data(self, session_data: pd.DataFrame) -> dict:
        """Combine all messages and metadata for a session"""
        meta_combined = []
        message_combined = []
        
        for _, row in session_data.iterrows():
            if pd.notna(row.get('$meta')):
                meta_combined.append(str(row['$meta']))
            if pd.notna(row.get('message')):
                message_combined.append(str(row['message']))
        
        return {
            'meta': ' | '.join(meta_combined),
            'message': ' | '.join(message_combined)
        }
    
    def process_with_flow(self, flow_module, flow_name: str, df: pd.DataFrame) -> dict:
        """Process data with a specific flow"""
        logger.info(f"Processing with {flow_name}")
        
        try:
            llm_client = flow_module.LLMClient(api_key=self.api_key)
            analyzer = flow_module.SessionAnalyzer(llm_client)
            
            results = analyzer.process_all_sessions(df)
            
            flow_results = {}
            for result in results:
                flow_results[result.session_id] = {
                    'title': result.title,
                    'summary': result.main_summary
                }
            
            logger.info(f"Completed {flow_name} processing for {len(results)} sessions")
            return flow_results
            
        except Exception as e:
            logger.error(f"Failed to process {flow_name}: {e}")
            return {}
    
    def process_csv_file(self, csv_file_path: str) -> pd.DataFrame:
        """Process the CSV file and generate results"""
        # Load and preprocess data
        df = self.load_and_preprocess_data(csv_file_path)
        
        # Process with all flows
        logger.info("Starting processing with all flows...")
        
        flow1_results = self.process_with_flow(flow1, "Flow1", df)
        flow2_results = self.process_with_flow(flow2, "Flow2", df)
        flow3_results = self.process_with_flow(flow3, "Flow3", df)
        flow4_results = self.process_with_flow(flow4, "Flow4", df)
        
        # Combine results
        combined_results = []
        
        sessions = df.groupby('session_id')
        for session_id, session_data in sessions:
            session_id = str(session_id)
            combined_data = self.combine_session_data(session_data)
            
            result_row = {
                'session_id': session_id,
                'meta': combined_data['meta'],
                'message': combined_data['message'],
                'flow1_title': flow1_results.get(session_id, {}).get('title', ''),
                'flow1_summary': flow1_results.get(session_id, {}).get('summary', ''),
                'flow2_title': flow2_results.get(session_id, {}).get('title', ''),
                'flow2_summary': flow2_results.get(session_id, {}).get('summary', ''),
                'flow3_title': flow3_results.get(session_id, {}).get('title', ''),
                'flow3_summary': flow3_results.get(session_id, {}).get('summary', ''),
                'flow4_title': flow4_results.get(session_id, {}).get('title', ''),
                'flow4_summary': flow4_results.get(session_id, {}).get('summary', '')
            }
            
            combined_results.append(result_row)
        
        # Create DataFrame from results
        results_df = pd.DataFrame(combined_results)
        
        # Save to CSV
        results_dir = Path("results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_csv_path = results_dir / f"results_{timestamp}.csv"
        
        results_df.to_csv(output_csv_path, index=False, encoding='utf-8')
        
        logger.info(f"Results saved to: {output_csv_path}")
        logger.info(f"Processed {len(combined_results)} sessions")
        
        return results_df

    def upload_embeddings_to_milvus(self, csv_file_path: str, collection_name: str = 'embeddings_collection'):
        """Upload embeddings from a CSV file to Milvus Cloud."""
        logger.info(f"Uploading embeddings from {csv_file_path} to Milvus Cloud collection '{collection_name}'")
        df = pd.read_csv(csv_file_path)
        if 'embedding' not in df.columns or 'id' not in df.columns:
            raise ValueError("CSV must contain 'id' and 'embedding' columns")
        # Parse embeddings
        df = df.dropna(subset=['embedding', 'id'])
        df['embedding'] = df['embedding'].apply(lambda x: ast.literal_eval(x) if isinstance(x, str) else x)
        # Connect to Milvus
        uri, user, password = read_milvus_credentials()
        client = MilvusClient(uri=uri, token=f"{user}:{password}")
        # Infer dimension
        dim = len(df['embedding'].iloc[0])
        # Create collection if not exists
        if not client.has_collection(collection_name):
            client.create_collection(
                collection_name=collection_name,
                dimension=dim,
                schema={
                    "fields": [
                        {"name": "id", "type": "VARCHAR", "is_primary": True, "max_length": 64},
                        {"name": "vector", "type": "FLOAT_VECTOR", "dim": dim}
                    ]
                },
                consistency_level="Strong"
            )
        # Prepare data
        data = [
            {"id": str(row['id']), "vector": row['embedding']} for _, row in df.iterrows()
        ]
        # Insert
        result = client.insert(collection_name=collection_name, data=data)
        logger.info(f"Inserted {result['insert_count']} embeddings into '{collection_name}'")
        return result

def main():
    # Initialize processor
    processor = CSVProcessor()
    
    # Process the CSV file
    csv_file_path = "sw_stm.query-17.csv"
    
    if not os.path.exists(csv_file_path):
        print(f"Error: CSV file '{csv_file_path}' not found!")
        return
    
    try:
        results_df = processor.process_csv_file(csv_file_path)
        print(f"Successfully processed {len(results_df)} sessions")
        print("Results saved to results/ folder")
        
        # Display sample results
        print("\nSample results:")
        print(results_df[['session_id', 'flow1_title', 'flow2_title']].head())
        
    except Exception as e:
        print(f"Error processing CSV: {e}")
        logger.error(f"Processing failed: {e}")

def upload_embeddings_cli():
    import argparse
    parser = argparse.ArgumentParser(description='Upload embeddings from CSV to Milvus Cloud')
    parser.add_argument('csv_file', help='Path to CSV file with id and embedding columns')
    parser.add_argument('--collection', default='embeddings_collection', help='Milvus collection name')
    args = parser.parse_args()
    processor = CSVProcessor()
    processor.upload_embeddings_to_milvus(args.csv_file, args.collection)

if __name__ == "__main__":
    main()
    # Uncomment to use CLI for uploading embeddings
    # upload_embeddings_cli() 