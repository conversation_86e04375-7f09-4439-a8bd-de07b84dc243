#!/usr/bin/env python3

import os
import json
from dotenv import load_dotenv
from openai import OpenAI
from flow1 import StrictMarkdownPromptEngine

# Load environment variables
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# Real Tesla data from your example
test_messages = '''{"summary": "", "entities": [], "reasoning": "", "addons": {}, "s3_link": null} | {"summary": "", "entities": {"company": ["Tesla"], "year": ["2024"]}, "reasoning": "", "addons": {}, "s3_link": null} | {"summary": "", "entities": [], "reasoning": "", "addons": {}, "s3_link": null}'''

test_meta = '''{"inactive":false,"thread_id":"97e454b9-d765-4b02-834f-c7840441b831","entities":{"company":["Tesla"],"year":["2023","2024"]},"context":"The conversation is focused on understanding Tesla's weekly order volumes in 2023 and 2024. The generated response provides a summary of Tesla's quarterly delivery figures for those years, indicating a decline in deliveries in 2024 compared to 2023. However, specific weekly order volume data is not available in the provided information.","question":"What was the weekly order volumes for Tesla in 2023 and 2024?"} | {"inactive":false,"thread_id":"00fbf0aa-4a60-4f3a-ac59-128c51f26839","entities":{"company":["Tesla"],"year":["2024"],"metric":["shipments","deliveries"]},"context":"The conversation is focused on analyzing Tesla's total vehicle shipments or deliveries in the year 2024. The generated response provides detailed information on Tesla's quarterly and annual delivery numbers for 2024, including a year-over-year comparison to 2023. The key points are that Tesla's total deliveries in 2024 were 1.79 million globally, a slight 1.1% decrease from the record-high 1.81 million vehicles delivered in 2023, marking the first annual decline in Tesla's deliveries after 12 consecutive years of growth.","question":"What were total shipments for Tesla in 2024?"} | {"inactive":false,"thread_id":"4869c1c5-7abb-415e-8479-9302ccfe9617","entities":{"company":["Tesla"],"time_period":["last 3 months","second quarter","third quarter","fourth quarter"]},"context":"The conversation is focused on analyzing Tesla's take rate, specifically over the past three months, broken down by lag period. The available information provides details about Tesla's overall delivery and production numbers, but does not contain the specific take rate metrics by lag that were requested.","question":"What is the take rate for Tesla in the last 3 months by lag?"}'''

# Parse the data to extract summaries, entities, reasoning
messages_parts = test_messages.split(' | ')
meta_parts = test_meta.split(' | ')

summaries = []
entities_list = []
reasoning_list = []

for part in messages_parts:
    try:
        data = json.loads(part)
        summaries.append(data.get('summary', ''))
        entities_list.append(str(data.get('entities', [])))
        reasoning_list.append(data.get('reasoning', ''))
    except:
        summaries.append('')
        entities_list.append('')
        reasoning_list.append('')

# Extract context information from meta
context_info = ""
for part in meta_parts:
    try:
        data = json.loads(part)
        if 'context' in data:
            context_info += data['context'] + "\n\n"
        if 'question' in data:
            context_info += f"QUESTION: {data['question']}\n\n"
    except:
        pass

business_context = f"Tesla demand forecasting and shipment analysis.\n\nEXTRACTED CONTEXT:\n{context_info}"

print("=== EXTRACTED CONTEXT ===")
print(business_context)
print("\n" + "="*50 + "\n")

# Create prompt engine
prompt_engine = StrictMarkdownPromptEngine()

# Generate prompt
prompt = prompt_engine.create_individual_summary_prompt(
    summaries, 
    entities_list, 
    reasoning_list, 
    business_context
)

print("=== PROMPT BEING SENT ===")
print(prompt[:1000] + "..." if len(prompt) > 1000 else prompt)
print("\n" + "="*50 + "\n")

# Get response from OpenAI
try:
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}],
        temperature=0.1
    )
    
    result = response.choices[0].message.content.strip()
    print("=== AI RESPONSE ===")
    print(result)
    
except Exception as e:
    print(f"Error: {e}")
