import pandas as pd
import json
import time
import logging
from typing import List, Dict, Any, Tu<PERSON>
from dataclasses import dataclass
import openai
import os

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SessionSummary:
    session_id: str
    individual_summaries: List[str]
    main_summary: str
    title: str
    message_count: int
    processing_time: float
    total_input_tokens: int
    total_output_tokens: int
    individual_input_tokens: List[int]
    individual_output_tokens: List[int]
    session_summary_input_tokens: int
    session_summary_output_tokens: int
    title_input_tokens: int
    title_output_tokens: int

class LLMClient:
    def __init__(self, api_key: str, model_name: str = "gpt-4.1-nano-2025-04-14"):
        self.api_key = api_key
        self.model_name = model_name
        self.client = openai.OpenAI(api_key=api_key)
    
    def count_tokens(self, prompt: str) -> int:
        return len(prompt.split()) * 1.3  # Rough estimate
    
    def generate_content(self, prompt: str, max_retries: int = 3) -> Tuple[str, int, int]:
        for attempt in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=300,  # Reduced from default to encourage shorter responses
                    temperature=0.7
                )
                
                content = response.choices[0].message.content.strip()
                input_tokens = response.usage.prompt_tokens
                output_tokens = response.usage.completion_tokens
                
                return content, input_tokens, output_tokens
                
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed: {e}")
                if attempt == max_retries - 1:
                    raise e
                time.sleep(2 ** attempt)

class AdvancedPromptEngine:
    @staticmethod
    def create_individual_summary_prompt(summaries: List[str], entities_list: List[str], reasoning_list: List[str], business_context: Dict[str, str] = None) -> str:
        context_info = ""
        if business_context:
            context_info = f"""
YAZAKI BUSINESS CONTEXT:
{business_context.get('context', '')}
Focus Area: {business_context.get('focus', '')}
Business Purpose: {business_context.get('business_purpose', '')}
"""

        return f"""
You are a Yazaki supply chain analyst specializing in automotive component supply management and customer demand analysis. Your task is to analyze conversation data and create concise, actionable summaries focused on Yazaki's business operations.

{context_info}

YAZAKI-SPECIFIC ANALYSIS PROTOCOL:

STEP 1: YAZAKI SUPPLY CHAIN CONTEXT ASSESSMENT
Analyze the conversation for Yazaki-relevant supply chain information:
- Identify customer orders, component requirements, and delivery schedules
- Extract part numbers, component specifications, and volume requirements  
- Note production planning impacts and capacity considerations
- Assess supplier performance and fulfillment effectiveness
- Identify any supply chain risks or opportunities for Yazaki operations

STEP 2: CUSTOMER RELATIONSHIP ANALYSIS
Evaluate customer-specific dynamics and requirements:
- Analyze customer demand patterns and ordering behavior
- Identify key automotive programs and production schedules
- Assess forecast accuracy and demand volatility
- Note any customer-specific requirements or constraints
- Evaluate Yazaki's performance in meeting customer expectations

STEP 3: OPERATIONAL INSIGHTS EXTRACTION
Focus on actionable insights for Yazaki operations:
- Identify efficiency improvements and process optimizations
- Extract lessons learned for future customer engagements
- Note best practices and successful fulfillment strategies
- Assess resource allocation and capacity planning needs
- Identify training or capability development opportunities

INPUT DATA FOR ANALYSIS:
OUTPUT/SUMMARY: {summaries[0] if summaries else "No summary available"}
ENTITIES: {entities_list[0] if entities_list else "No entities identified"}
REASONING: {reasoning_list[0] if reasoning_list else "No reasoning provided"}

CRITICAL OUTPUT REQUIREMENTS:

INSUFFICIENT CONTEXT HANDLING:
If the conversation lacks sufficient detail to provide meaningful Yazaki-specific insights, state:
"**Insufficient Context Available**: The conversation does not contain enough detail to generate meaningful supply chain insights for Yazaki operations. Additional information about [specific missing elements] would be needed for comprehensive analysis."

YAZAKI-FOCUSED CONTENT REQUIREMENTS:
- Generate output in markdown format with bullet points only
- Maximum 3-4 bullet points, each 15-20 words maximum
- Focus specifically on Yazaki supply chain operations and customer relationships
- Use automotive industry terminology familiar to Yazaki teams
- Avoid generic supply chain jargon - be specific to Yazaki's business model
- Include specific part numbers, customer names, or quantitative data when available

MARKDOWN FORMAT EXAMPLE:
• **Customer Impact**: [specific insight about customer relationship or demand]
• **Supply Chain Action**: [specific operational improvement or concern]  
• **Performance Metric**: [quantitative result or target, if available]
• **Next Steps**: [specific recommendation for Yazaki teams]

Execute the analysis and provide only the final markdown summary as specified above.
"""

    @staticmethod
    def create_session_summary_prompt(individual_summaries: List[str], session_id: str, business_context: Dict[str, str] = None) -> str:
        summaries_text = "\n\n".join([f"SUMMARY {i+1}:\n{summary}" for i, summary in enumerate(individual_summaries)])
        
        context_info = ""
        if business_context:
            context_info = f"""
YAZAKI BUSINESS CONTEXT:
{business_context.get('context', '')}
Focus Area: {business_context.get('focus', '')}
Business Purpose: {business_context.get('business_purpose', '')}
"""

        return f"""
You are a senior Yazaki supply chain strategist responsible for synthesizing customer conversations into actionable intelligence for automotive component supply operations.

{context_info}

YAZAKI STRATEGIC SYNTHESIS PROTOCOL:

STEP 1: MULTI-CONVERSATION INTEGRATION
Synthesize insights across all conversation summaries:
- Identify recurring customer themes and supply chain patterns
- Extract consolidated customer requirements and expectations
- Note consistent operational challenges or opportunities
- Assess overall customer relationship health and performance trends
- Identify strategic implications for Yazaki's automotive business

STEP 2: STRATEGIC PRIORITY IDENTIFICATION  
Determine the most critical insights for Yazaki operations:
- Prioritize customer satisfaction and retention factors
- Identify immediate operational actions required
- Assess resource allocation and capacity planning needs
- Note competitive positioning and market opportunities
- Evaluate risk factors requiring management attention

STEP 3: ACTIONABLE RECOMMENDATIONS
Formulate specific next steps for Yazaki teams:
- Provide clear operational recommendations with owners
- Suggest process improvements and efficiency gains
- Recommend customer engagement strategies
- Identify follow-up actions and timelines
- Propose performance metrics and success measures

INPUT DATA FOR STRATEGIC SYNTHESIS:
{summaries_text}

CRITICAL OUTPUT REQUIREMENTS:

INSUFFICIENT CONTEXT HANDLING:
If the conversation summaries lack sufficient detail for strategic synthesis, state:
"**Insufficient Context Available**: The conversation summaries do not contain enough strategic detail for comprehensive Yazaki supply chain analysis. Additional information about [specific missing strategic elements] would be needed for actionable recommendations."

YAZAKI STRATEGIC CONTENT REQUIREMENTS:
- Generate output in markdown format with bullet points only
- Maximum 4-5 bullet points, each 20-25 words maximum
- Focus on strategic implications for Yazaki's automotive supply chain
- Include specific customer names, part families, or business metrics when available
- Emphasize actionable next steps with clear ownership
- Use Yazaki-familiar automotive terminology and business language

MARKDOWN FORMAT EXAMPLE:
• **Customer Strategy**: [key customer relationship insight or action needed]
• **Operational Priority**: [critical supply chain action or improvement required]
• **Business Impact**: [quantitative or qualitative business result expected]
• **Resource Allocation**: [specific resource or capability need identified]
• **Timeline**: [recommended timeline for key actions or decisions]

Execute the strategic synthesis and provide only the final markdown summary as specified above.
"""

    @staticmethod
    def create_title_generation_prompt(main_summary: str, session_id: str, business_context: Dict[str, str] = None) -> str:
        context_info = ""
        if business_context:
            context_info = f"Business Context: {business_context.get('focus', '')}"

        return f"""
You are a Yazaki business analyst creating concise titles for supply chain conversation analysis.

{context_info}

TITLE GENERATION REQUIREMENTS:
- Create a 4-8 word title that captures the essential Yazaki business focus
- Use automotive industry terminology familiar to Yazaki teams  
- Include customer name or key component when relevant
- Focus on the primary business action or outcome
- Avoid generic terms - be specific to the conversation content

SESSION SUMMARY FOR TITLE GENERATION:
{main_summary}

Provide only the title in plain text without quotation marks or additional commentary.
"""

class SessionAnalyzer:
    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client
        self.prompt_engine = AdvancedPromptEngine()
        self.business_context = None
        
    def load_and_preprocess_data(self, file_path: str) -> pd.DataFrame:
        logger.info(f"Loading data from {file_path}")
        
        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            df = pd.read_excel(file_path)
        else:
            df = pd.read_csv(file_path)
            
        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        df = df[df['role'] != 'human']
        
        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df
        
    def extract_message_fields(self, message_string: str) -> Dict[str, str]:
        try:
            if pd.isna(message_string) or message_string == '':
                return {"summary": "No summary available", "reasoning": "No reasoning available"}
            
            try:
                message_data = json.loads(message_string)
                summary = message_data.get('summary', message_data.get('output', ''))
                reasoning = message_data.get('reasoning', '')
                entities = message_data.get('entities', '')
                
                return {
                    "summary": summary if summary else "No summary available",
                    "reasoning": reasoning if reasoning else "No reasoning available",
                    "entities": entities if entities else "No entities identified"
                }
                
            except json.JSONDecodeError:
                return {
                    "summary": str(message_string),
                    "reasoning": "No structured reasoning available",
                    "entities": "No entities identified"
                }
                
        except Exception as e:
            logger.warning(f"Error extracting message fields: {e}")
            return {"summary": "Error processing message", "reasoning": "Error in reasoning extraction", "entities": "Error in entity extraction"}
    
    def extract_entities(self, meta_string: str) -> str:
        try:
            if pd.isna(meta_string):
                return "No entities available"
                
            meta_data = json.loads(meta_string)
            entities = meta_data.get('entities', {})
            
            if not entities:
                return "No entities identified"
            
            entity_parts = []
            for entity_type, entity_list in entities.items():
                if entity_list:
                    entity_parts.append(f"{entity_type}: {', '.join(entity_list)}")
            
            return '; '.join(entity_parts) if entity_parts else "No entities identified"
            
        except Exception as e:
            logger.warning(f"Error extracting entities: {e}")
            return "Error extracting entities"
    
    def generate_individual_summary(self, row: pd.Series) -> Tuple[str, int, int]:
        message_fields = self.extract_message_fields(row.get('message', ''))
        entities = self.extract_entities(row.get('$meta', ''))
        
        prompt = self.prompt_engine.create_individual_summary_prompt(
            [message_fields['summary']], 
            [entities], 
            [message_fields['reasoning']],
            self.business_context
        )
        
        return self.llm_client.generate_content(prompt)
    
    def generate_session_summary(self, individual_summaries: List[str], session_id: str) -> Tuple[str, int, int]:
        prompt = self.prompt_engine.create_session_summary_prompt(
            individual_summaries, 
            session_id,
            self.business_context
        )
        
        return self.llm_client.generate_content(prompt)
    
    def generate_session_title(self, main_summary: str, session_id: str) -> Tuple[str, int, int]:
        prompt = self.prompt_engine.create_title_generation_prompt(
            main_summary, 
            session_id,
            self.business_context
        )
        
        return self.llm_client.generate_content(prompt)
    
    def analyze_session(self, session_data: pd.DataFrame, session_id: str) -> SessionSummary:
        start_time = time.time()
        
        individual_summaries = []
        individual_input_tokens = []
        individual_output_tokens = []
        
        for _, row in session_data.iterrows():
            summary, input_tokens, output_tokens = self.generate_individual_summary(row)
            individual_summaries.append(summary)
            individual_input_tokens.append(input_tokens)
            individual_output_tokens.append(output_tokens)
        
        main_summary, session_input_tokens, session_output_tokens = self.generate_session_summary(individual_summaries, session_id)
        
        title, title_input_tokens, title_output_tokens = self.generate_session_title(main_summary, session_id)
        
        processing_time = time.time() - start_time
        
        return SessionSummary(
            session_id=session_id,
            individual_summaries=individual_summaries,
            main_summary=main_summary,
            title=title,
            message_count=len(session_data),
            processing_time=processing_time,
            total_input_tokens=sum(individual_input_tokens) + session_input_tokens + title_input_tokens,
            total_output_tokens=sum(individual_output_tokens) + session_output_tokens + title_output_tokens,
            individual_input_tokens=individual_input_tokens,
            individual_output_tokens=individual_output_tokens,
            session_summary_input_tokens=session_input_tokens,
            session_summary_output_tokens=session_output_tokens,
            title_input_tokens=title_input_tokens,
            title_output_tokens=title_output_tokens
        )
    
    def process_all_sessions(self, df: pd.DataFrame) -> List[SessionSummary]:
        sessions = df.groupby('session_id')
        results = []
        
        for session_id, session_data in sessions:
            try:
                logger.info(f"Processing session {session_id}")
                session_summary = self.analyze_session(session_data, str(session_id))
                results.append(session_summary)
                
            except Exception as e:
                logger.error(f"Error processing session {session_id}: {e}")
                continue
        
        return results

def load_api_key() -> str:
    from dotenv import load_dotenv
    load_dotenv()
    return os.getenv('OPENAI_API_KEY') 