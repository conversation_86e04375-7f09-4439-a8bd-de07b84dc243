#!/usr/bin/env python3

import os
from dotenv import load_dotenv
from openai import OpenAI
from flow1 import StrictMarkdownPromptEngine

# Load environment variables
load_dotenv()

# Initialize OpenAI client
client = OpenAI(api_key=os.getenv('OPENAI_API_KEY'))

# Test data with Tesla context information
test_summaries = ["", "", ""]  # Empty summaries
test_entities = ["", "", ""]   # Empty entities
test_reasoning = ["", "", ""]  # Empty reasoning

# Business context with quantitative data
test_context = """Tesla demand forecasting and shipment analysis. 

CONTEXT DATA:
- Tesla's total deliveries in 2024 were 1.79 million globally
- 1.1% decrease from the record-high 1.81 million vehicles delivered in 2023
- This marks the first annual decline in Tesla's deliveries after 12 consecutive years of growth
- Quarterly delivery data available for Q1, Q2, Q3, Q4 2024
- Weekly order volume data for 2023-2024 comparison was requested
- Take rate analysis for last 3 months by lag period was requested"""

# Create prompt engine
prompt_engine = StrictMarkdownPromptEngine()

# Generate prompt
prompt = prompt_engine.create_individual_summary_prompt(
    test_summaries, 
    test_entities, 
    test_reasoning, 
    test_context
)

print("=== PROMPT BEING SENT ===")
print(prompt)
print("\n" + "="*50 + "\n")

# Get response from OpenAI
try:
    response = client.chat.completions.create(
        model="gpt-4o-mini",
        messages=[{"role": "user", "content": prompt}],
        temperature=0.1
    )
    
    result = response.choices[0].message.content.strip()
    print("=== AI RESPONSE ===")
    print(result)
    
except Exception as e:
    print(f"Error: {e}")
