import pandas as pd
import json
import time
import logging
from typing import List, Dict, Any
import sys
import os
from datetime import datetime
from flask import Flask, request, jsonify
from werkzeug.utils import secure_filename
import io
import pandas as pd
import os
from datetime import datetime
from pathlib import Path

import flow1
import flow2
import flow3
import flow4

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024

class CombinedFlowProcessor:
    def __init__(self):
        self.api_key = self.load_api_key()
        
        if not self.api_key:
            raise ValueError("No API key found in .env file")
            
        logger.info("API key loaded successfully")
        
    def load_api_key(self) -> str:
        from dotenv import load_dotenv
        load_dotenv()
        return os.getenv('OPENAI_API_KEY')
    
    def load_and_preprocess_data_from_file(self, file_path: str) -> pd.DataFrame:
        logger.info(f"Loading data from {file_path}")
        
        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            df = pd.read_excel(file_path)
        else:
            df = pd.read_csv(file_path)
            
        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        df = df[df['role'] != 'human']
        
        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df
    
    def load_and_preprocess_data_from_json(self, data: List[Dict]) -> pd.DataFrame:
        logger.info("Loading data from JSON")
        
        df = pd.DataFrame(data)
        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        df = df[df['role'] != 'human']
        
        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df
    
    def combine_session_data(self, session_data: pd.DataFrame) -> Dict[str, str]:
        meta_combined = []
        message_combined = []
        
        for _, row in session_data.iterrows():
            if pd.notna(row.get('$meta')):
                meta_combined.append(str(row['$meta']))
            if pd.notna(row.get('message')):
                message_combined.append(str(row['message']))
        
        return {
            'meta': ' | '.join(meta_combined),
            'message': ' | '.join(message_combined)
        }
    
    def process_with_flow(self, flow_module, flow_name: str, df: pd.DataFrame) -> Dict[str, Dict[str, str]]:
        logger.info(f"Processing with {flow_name}")
        
        try:
            llm_client = flow_module.LLMClient(api_key=self.api_key)
            analyzer = flow_module.SessionAnalyzer(llm_client)
            
            results = analyzer.process_all_sessions(df)
            
            flow_results = {}
            for result in results:
                flow_results[result.session_id] = {
                    'title': result.title,
                    'summary': result.main_summary
                }
            
            logger.info(f"Completed {flow_name} processing for {len(results)} sessions")
            return flow_results
            
        except Exception as e:
            logger.error(f"Failed to process {flow_name}: {e}")
            return {}
    
    def process_all_flows(self, df: pd.DataFrame) -> Dict[str, Any]:
        flow1_results = self.process_with_flow(flow1, "Flow1", df)
        time.sleep(10)
        flow2_results = self.process_with_flow(flow2, "Flow2", df)
        time.sleep(10)
        flow3_results = self.process_with_flow(flow3, "Flow3", df)
        time.sleep(10)
        flow4_results = self.process_with_flow(flow4, "Flow4", df)
        time.sleep(10)
        
        combined_results = []
        
        sessions = df.groupby('session_id')
        for session_id, session_data in sessions:
            session_id = str(session_id)
            combined_data = self.combine_session_data(session_data)
            
            result_row = {
                'session_id': session_id,
                'meta': combined_data['meta'],
                'message': combined_data['message'],
                'flow1_title': flow1_results.get(session_id, {}).get('title', ''),
                'flow1_summary': flow1_results.get(session_id, {}).get('summary', ''),
                'flow2_title': flow2_results.get(session_id, {}).get('title', ''),
                'flow2_summary': flow2_results.get(session_id, {}).get('summary', ''),
                'flow3_title': flow3_results.get(session_id, {}).get('title', ''),
                'flow3_summary': flow3_results.get(session_id, {}).get('summary', ''),
                'flow4_title': flow4_results.get(session_id, {}).get('title', ''),
                'flow4_summary': flow4_results.get(session_id, {}).get('summary', '')
            }
            
            combined_results.append(result_row)
        
        return {
            'status': 'success',
            'total_sessions': len(combined_results),
            'processing_time': datetime.now().isoformat(),
            'results': combined_results
        }

processor = CombinedFlowProcessor()

def generate_csv(data):
    results = data.get('results', [])
    
    if not results:
        print("No results found in the JSON data")
        return None
        
    print(f"Processing {len(results)} records...")
    
    csv_rows = []
    
    for i, result in enumerate(results):
        row = {
            'session_id': result.get('session_id', ''),
            'flow1_title': result.get('flow1_title', ''),
            'flow1_summary': result.get('flow1_summary', ''),
            'flow2_title': result.get('flow2_title', ''),
            'flow2_summary': result.get('flow2_summary', ''),
            'flow3_title': result.get('flow3_title', ''),
            'flow3_summary': result.get('flow3_summary', ''),
            'flow4_title': result.get('flow4_title', ''),
            'flow4_summary': result.get('flow4_summary', ''),
            'message': result.get('message', ''),
            'meta': result.get('meta', '')
        }
        csv_rows.append(row)
    
    df = pd.DataFrame(csv_rows)
    
    results_dir = Path("results")
    results_dir.mkdir(exist_ok=True)
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    output_csv_path = results_dir / f"results_{timestamp}.csv"
    
    df.to_csv(output_csv_path, index=False, encoding='utf-8')
    
    print(f"CSV file saved to: {output_csv_path}")
    
    return df

@app.route('/process/data', methods=['POST'])
def process_data():
    try:
        if not request.is_json:
            return jsonify({'error': 'Content-Type must be application/json'}), 400
        
        data = request.get_json()
        
        if not isinstance(data, list):
            return jsonify({'error': 'Data must be a list of records'}), 400
        
        if len(data) == 0:
            return jsonify({'error': 'Data cannot be empty'}), 400
        
        required_fields = ['session_id', 'message', 'role']
        for record in data:
            for field in required_fields:
                if field not in record:
                    return jsonify({'error': f'Missing required field: {field}'}), 400
        
        df = processor.load_and_preprocess_data_from_json(data)
        results = processor.process_all_flows(df)
        
        generate_csv(results)
        return jsonify(results)
        
    except Exception as e:
        logger.error(f"Error processing data: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/health', methods=['GET'])
def health_check():
    return jsonify({'status': 'healthy', 'timestamp': datetime.now().isoformat()})

if __name__ == '__main__':
    port = int(os.environ.get('PORT', 5000))
    debug = os.environ.get('DEBUG', 'False').lower() == 'true'
    app.run(host='0.0.0.0', port=port, debug=debug)