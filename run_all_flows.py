import pandas as pd
import json
import time
import logging
from typing import List, Dict, Any
import sys
import os
from datetime import datetime
from pathlib import Path

import flow1
import flow2
import flow3
import flow4

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiFlowProcessor:
    def __init__(self):
        self.api_key = self.load_api_key()
        
        if not self.api_key:
            raise ValueError("No API key found in .env file")
            
        logger.info("API key loaded successfully")
        
        # Initialize all flows
        self.flows = {
            "Flow1": flow1.SessionAnalyzer(flow1.LLMClient(self.api_key)),
            "Flow2": flow2.SessionAnalyzer(flow2.LLMClient(self.api_key)),
            "Flow3": flow3.SessionAnalyzer(flow3.LLMClient(self.api_key)),
            "Flow4": flow4.SessionAnalyzer(flow4.LLMClient(self.api_key))
        }
        
        # Define business context for each NP test file
        self.np_test_contexts = {
            "NP_test_01.csv": {
                "context": "Tesla demand and fulfillment trends analysis across 2023 and 2024. Focus on year-over-year order volume dynamics, total shipment activity, and anomalies between forecasted and actual orders for forecast alignment with Tesla planners.",
                "focus": "Tesla demand forecasting and shipment analysis",
                "purpose": "Support forecast alignment call with Tesla planning team"
            },
            "NP_test_02.csv": {
                "context": "Rivian R1S vehicle line supply performance and demand concentration assessment. Focus on identifying high-volume parts, sustained demand components, and fulfillment effectiveness analysis including detailed review of part PT00876922-E.",
                "focus": "Rivian R1S supply performance and key component analysis", 
                "purpose": "Optimize supply chain for high-volume Rivian components"
            },
            "NP_test_03.csv": {
                "context": "Model 3 H LV carline demand landscape comprehensive analysis. Focus on historical customer ordering patterns, recent Tesla news impact, and external factors influencing demand or production planning.",
                "focus": "Model 3 H LV demand patterns and market influence analysis",
                "purpose": "Enhance demand planning with market intelligence integration"
            },
            "NP_test_04.csv": {
                "context": "Tesla recent order-to-fulfillment performance evaluation. Focus on demand conversion to shipments over time, identifying fulfillment inefficiencies or improvements in the supply chain process.",
                "focus": "Tesla order fulfillment performance optimization",
                "purpose": "Improve demand-to-shipment conversion rates"
            },
            "NP_test_05.csv": {
                "context": "SupplyWhy crystal ball projection forecast accuracy evaluation versus EDI demand signals for Cyber Truck HV carline. Focus on forecast reliability assessment and demand signal validation.",
                "focus": "Cyber Truck HV forecast accuracy vs EDI demand analysis",
                "purpose": "Validate and improve forecasting model performance"
            }
        }
        
    def load_api_key(self) -> str:
        try:
            from dotenv import load_dotenv
            load_dotenv()
            return os.getenv('OPENAI_API_KEY')
        except ImportError:
            return os.getenv('OPENAI_API_KEY')
    
    def load_and_preprocess_data(self, file_path: str) -> pd.DataFrame:
        logger.info(f"Loading data from {file_path}")
        
        df = pd.read_csv(file_path)
        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        df = df[df['role'] != 'human']
        
        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df

    def process_file_with_all_flows(self, file_path: str):
        logger.info(f"\n{'='*50}")
        logger.info(f"PROCESSING: {file_path}")
        logger.info(f"{'='*50}")
        
        # Get business context for this file
        test_config = self.np_test_contexts.get(file_path, {})
        business_context = test_config.get('context', '')
        
        logger.info(f"Business Context: {test_config.get('focus', 'General supply chain analysis')}")
        
        # Load data
        df = self.load_and_preprocess_data(file_path)
        
        all_results = []
        
        # Process with each flow
        for flow_name, flow_analyzer in self.flows.items():
            logger.info(f"\n--- Processing with {flow_name} ---")
            
            try:
                # Process all sessions with this flow
                session_summaries = flow_analyzer.process_all_sessions(df)
                
                # Add flow information to results
                for summary in session_summaries:
                    summary.flow_name = flow_name
                    summary.source_file = file_path
                    summary.business_context = business_context
                
                all_results.extend(session_summaries)
                logger.info(f"✓ Completed {flow_name} - {len(session_summaries)} sessions processed")
                
            except Exception as e:
                logger.error(f"✗ Failed to process with {flow_name}: {str(e)}")
                continue
        
        return all_results

    def save_results(self, all_results: List, file_path: str):
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"all_flows_results_{timestamp}.csv"
        output_path = os.path.join("results", filename)
        
        os.makedirs("results", exist_ok=True)
        
        results_data = []
        for summary in all_results:
            # Join individual summaries with markdown formatting preserved
            individual_summaries_text = "\n\n---\n\n".join(summary.individual_summaries)
            
            results_data.append({
                'session_id': summary.session_id,
                'flow_name': getattr(summary, 'flow_name', 'Unknown'),
                'title': summary.title,
                'main_summary': summary.main_summary,  # This is already in markdown format
                'individual_summaries': individual_summaries_text,  # This preserves markdown formatting
                'message_count': summary.message_count,
                'processing_time': summary.processing_time,
                'total_input_tokens': summary.total_input_tokens,
                'total_output_tokens': summary.total_output_tokens,
                'source_file': getattr(summary, 'source_file', file_path),
                'business_context': getattr(summary, 'business_context', '')
            })
        
        results_df = pd.DataFrame(results_data)
        results_df.to_csv(output_path, index=False)
        logger.info(f"Results saved to {output_path}")
        return output_path

def main():
    processor = MultiFlowProcessor()
    
    # List of NP test files to process
    np_test_files = [
        "NP_test_01.csv",
        "NP_test_02.csv", 
        "NP_test_03.csv",
        "NP_test_04.csv",
        "NP_test_05.csv"
    ]
    
    all_results = []
    
    logger.info("Starting NP Test Files Processing with All 5 Flows...")
    logger.info(f"Files to process: {len(np_test_files)}")
    logger.info(f"Flows to run: {list(processor.flows.keys())}")
    
    for file_path in np_test_files:
        if os.path.exists(file_path):
            try:
                results = processor.process_file_with_all_flows(file_path)
                all_results.extend(results)
                logger.info(f"✓ Successfully processed {file_path}")
            except Exception as e:
                logger.error(f"✗ Failed to process {file_path}: {str(e)}")
                continue
        else:
            logger.warning(f"⚠ File not found: {file_path}")
    
    # Save all results
    if all_results:
        output_path = processor.save_results(all_results, "all_files")
        logger.info(f"\n{'='*50}")
        logger.info(f"PROCESSING COMPLETE")
        logger.info(f"{'='*50}")
        logger.info(f"Total sessions processed: {len(all_results)}")
        logger.info(f"Results saved to: {output_path}")
    else:
        logger.error("No results generated!")

if __name__ == "__main__":
    main() 