import pandas as pd
import json
import time
import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass
import openai
from pathlib import Path
import sys
import os
from datetime import datetime
from dotenv import load_dotenv

class StrictMarkdownPromptEngine:
    @staticmethod
    def create_individual_summary_prompt(summaries: List[str], entities_list: List[str], reasoning_list: List[str], business_context: str = "") -> str:
        summaries_text = "\n\n".join([f"ANALYSIS {i+1}: {summary}" for i, summary in enumerate(summaries)])
        entities_text = "\n".join([f"ENTITIES {i+1}: {entities}" for i, entities in enumerate(entities_list) if entities])
        reasoning_text = "\n\n".join([f"REASONING {i+1}: {reasoning}" for i, reasoning in enumerate(reasoning_list) if reasoning])
        
        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""
        
        return f"""
You are a Yazaki supply chain analyst. You MUST output ONLY markdown format with bullet points.

STRICT MARKDOWN OUTPUT REQUIREMENTS:
- You MUST start with "## Individual Analysis"
- You MUST use ONLY bullet points with "•" symbol
- You MUST use markdown bold formatting with "**" for key terms
- You MUST have exactly 3 bullet points maximum
- Each bullet point MUST be 15 words or less
- If insufficient data, output: "• **Insufficient Data**: Cannot generate meaningful analysis without additional context"

REQUIRED MARKDOWN FORMAT:
## Individual Analysis

• **Key Finding**: [Main insight about component demand/supply]
• **Impact**: [Effect on production planning or customer fulfillment]  
• **Action Required**: [Specific next steps for supply chain team]

YAZAKI-SPECIFIC FOCUS:
- Focus on automotive component demand patterns, supply chain performance, and fulfillment metrics
- Use Yazaki terminology: components, harnesses, connectors, assemblies, production lines
- Emphasize customer relationships (Tesla, Rivian, etc.) and part numbers when available
- Address supply chain visibility, demand signals, and production planning specifics

CONVERSATION DATA:
{summaries_text}

{entities_text if entities_text else ""}

{reasoning_text if reasoning_text else ""}
{context_section}

CRITICAL: Output ONLY the markdown format as specified above. Do not include any other text, explanations, or formatting.
"""

    @staticmethod
    def create_session_summary_prompt(individual_summaries: List[str], session_id: str, business_context: str = "") -> str:
        summaries_text = "\n\n".join([f"SUMMARY {i+1}:\n{summary}" for i, summary in enumerate(individual_summaries)])
        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""
        
        return f"""
You are a Yazaki supply chain director. You MUST output ONLY markdown format with bullet points.

STRICT MARKDOWN OUTPUT REQUIREMENTS:
- You MUST start with "## Supply Chain Analysis Summary"
- You MUST use ONLY bullet points with "•" symbol
- You MUST use markdown bold formatting with "**" for key terms
- You MUST have exactly 3 bullet points maximum
- Each bullet point MUST be 20 words or less
- If insufficient data, output: "• **Data Limitation**: Analysis incomplete due to insufficient context in source conversations"

REQUIRED MARKDOWN FORMAT:
## Supply Chain Analysis Summary

• **Primary Issue**: [Main supply chain challenge or opportunity]
• **Customer Impact**: [Specific effect on key automotive customers]
• **Recommended Action**: [Executive decision required for supply chain optimization]

EXECUTIVE SYNTHESIS REQUIREMENTS:
- Consolidate individual summaries into cohesive supply chain insights
- Focus on Yazaki's automotive component business impact
- Highlight customer-specific issues (Tesla, Rivian, etc.) and part number analysis
- Address demand forecasting, production planning, and fulfillment performance

INDIVIDUAL SUMMARIES:
{summaries_text}
{context_section}

CRITICAL: Output ONLY the markdown format as specified above. Do not include any other text, explanations, or formatting.
"""

    @staticmethod
    def create_title_generation_prompt(main_summary: str, session_id: str, business_context: str = "") -> str:
        context_section = f"\n\nBUSINESS CONTEXT:\n{business_context}" if business_context else ""
        
        return f"""
Generate a concise title for this Yazaki supply chain analysis session.

TITLE REQUIREMENTS:
- 3-6 words maximum
- Include customer name (Tesla, Rivian, etc.) when relevant
- Focus on supply chain action: Analysis, Forecast, Optimization, Review
- Use Yazaki terminology: Components, Demand, Fulfillment, Supply

EXAMPLES:
- "Tesla Component Demand Analysis"
- "Rivian Supply Chain Review" 
- "Component Forecast Optimization"

SESSION DATA:
{main_summary}
{context_section}

CRITICAL: Output ONLY the title. Do not include any other text, explanations, or formatting.
"""

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

@dataclass
class SessionSummary:
    session_id: str
    individual_summaries: List[str]
    main_summary: str
    title: str
    message_count: int
    processing_time: float
    total_input_tokens: int
    total_output_tokens: int
    individual_input_tokens: List[int]
    individual_output_tokens: List[int]
    session_summary_input_tokens: int
    session_summary_output_tokens: int
    title_input_tokens: int
    title_output_tokens: int

class LLMClient:
    def __init__(self, api_key: str, model_name: str = "gpt-4.1-nano-2025-04-14"):
        self.api_key = api_key
        self.model_name = model_name
        self.client = openai.OpenAI(api_key=api_key)
        self.prompt_engine = StrictMarkdownPromptEngine()
        
    def count_tokens(self, prompt: str) -> int:
        return len(prompt.split()) * 1.3
        
    def generate_content(self, prompt: str, max_retries: int = 3) -> Tuple[str, int, int]:
        input_tokens = self.count_tokens(prompt)
        
        for attempt in range(max_retries):
            try:
                response = self.client.chat.completions.create(
                    model=self.model_name,
                    messages=[{"role": "user", "content": prompt}],
                    max_tokens=4000,
                    temperature=0.7
                )
                
                output_tokens = response.usage.completion_tokens if response.usage else 0
                input_tokens = response.usage.prompt_tokens if response.usage else input_tokens
                
                logger.info(f"Token usage - Input: {input_tokens}, Output: {output_tokens}")
                return response.choices[0].message.content, input_tokens, output_tokens
                
            except Exception as e:
                logger.error(f"Error generating content (attempt {attempt + 1}): {e}")
                if attempt == max_retries - 1:
                    raise
                time.sleep(2)

class AdvancedPromptEngine:
    @staticmethod
    def create_individual_summary_prompt(summaries: List[str], entities_list: List[str], reasoning_list: List[str]) -> str:
        return f"""
You are a Yazaki supply chain analyst specializing in automotive component analysis. You MUST output ONLY markdown format with bullet points.

STRICT MARKDOWN OUTPUT REQUIREMENTS:
- You MUST start with "## Individual Analysis"
- You MUST use ONLY bullet points with "•" symbol
- You MUST use markdown bold formatting with "**" for key terms
- You MUST have exactly 3 bullet points maximum
- Each bullet point MUST be 15 words or less
- If insufficient data, output: "• **Insufficient Data**: Cannot generate meaningful analysis without additional context"

REQUIRED MARKDOWN FORMAT:
## Individual Analysis

• **Key Finding**: [Main insight about component demand/supply]
• **Impact**: [Effect on production planning or customer fulfillment]  
• **Action Required**: [Specific next steps for supply chain team]

YAZAKI-SPECIFIC FOCUS:
- Focus on automotive component demand patterns, supply chain performance, and fulfillment metrics
- Use Yazaki terminology: components, harnesses, connectors, assemblies, production lines
- Emphasize customer relationships (Tesla, Rivian, etc.) and part numbers when available
- Address supply chain visibility, demand signals, and production planning specifics

INPUT DATA FOR ANALYSIS:
OUTPUT/SUMMARY: {summaries[0]}
ENTITIES: {entities_list[0]}
REASONING: {reasoning_list[0]}

CRITICAL: Output ONLY the markdown format as specified above. Do not include any other text, explanations, or formatting.
"""

    @staticmethod
    def create_session_summary_prompt(individual_summaries: List[str], session_id: str) -> str:
        summaries_text = "\n\n".join([f"SUMMARY {i+1}:\n{summary}" for i, summary in enumerate(individual_summaries)])
        
        return f"""
You are a Yazaki supply chain director. You MUST output ONLY markdown format with bullet points.

STRICT MARKDOWN OUTPUT REQUIREMENTS:
- You MUST start with "## Supply Chain Analysis Summary"
- You MUST use ONLY bullet points with "•" symbol
- You MUST use markdown bold formatting with "**" for key terms
- You MUST have exactly 3 bullet points maximum
- Each bullet point MUST be 20 words or less
- If insufficient data, output: "• **Data Limitation**: Analysis incomplete due to insufficient context in source conversations"

REQUIRED MARKDOWN FORMAT:
## Supply Chain Analysis Summary

• **Primary Issue**: [Main supply chain challenge or opportunity]
• **Customer Impact**: [Specific effect on key automotive customers]
• **Recommended Action**: [Executive decision required for supply chain optimization]

EXECUTIVE SYNTHESIS REQUIREMENTS:
- Consolidate individual summaries into cohesive supply chain insights
- Focus on Yazaki's automotive component business impact
- Highlight customer-specific issues (Tesla, Rivian, etc.) and part number analysis
- Address demand forecasting, production planning, and fulfillment performance

INDIVIDUAL SUMMARIES:
{summaries_text}

CRITICAL: Output ONLY the markdown format as specified above. Do not include any other text, explanations, or formatting.
"""

    @staticmethod
    def create_title_generation_prompt(main_summary: str, session_id: str) -> str:
        return f"""
You are an expert information architect who specializes in content analysis and precise title generation for executive-level strategic documentation. Your expertise lies in distilling complex strategic business content containing specific entities into concise, meaningful titles that immediately communicate business value and include key entity references.

COMPREHENSIVE STRATEGIC TITLE GENERATION PROTOCOL WITH ENTITY INCLUSION:

STEP 1: DEEP STRATEGIC CONTENT ANALYSIS AND ENTITY EXTRACTION
Identify primary entities and strategic themes within the summary:
- Parse complete summary content to identify specific products, technologies, companies, people, and initiatives mentioned
- Extract central business challenge, market opportunity, competitive threat, or transformation objective involving particular entities
- Identify specific type of strategic activity including planning, optimization, implementation, or analysis mentioning key entities
- Determine organizational scope and impact level referencing specific departments, business units, or market segments
- Assess strategic impact level while considering specific technologies, products, or organizational changes mentioned
- Recognize primary stakeholders, business units, and market participants by their actual names or designations
- Map geographic scope, temporal context, and market conditions involving specific regions, timeframes, or market segments
- Extract industry sector specifics, business function details, and market segment considerations with concrete entity references
- Evaluate urgency level, timeline sensitivity, and implementation priority mentioning specific deadlines, milestones, or phases
- Identify regulatory, compliance, or governance context affecting particular entities, frameworks, or requirements

STEP 2: STRATEGIC SIGNIFICANCE AND BUSINESS OUTCOME EVALUATION WITH ENTITY FOCUS
Assess strategic importance while preserving entity specifics:
- Determine strategic importance, competitive significance, and market impact involving specific entities, products, or initiatives
- Identify measurable business outcomes including financial performance, operational efficiency, and competitive advantage with specific metrics
- Evaluate innovation elements, transformation aspects, and capability development involving particular technologies, systems, or processes
- Assess risk mitigation effectiveness, opportunity capture potential, and performance enhancement with concrete examples
- Extract future-state vision, strategic objectives, and target outcomes mentioning specific goals, KPIs, or success criteria
- Classify primary type of strategic decision, organizational transformation, or market positioning initiative with entity details
- Determine strategic decision-making level including operational tactics, strategic initiatives, or industry leadership with specific scope
- Identify whether strategic focus is on assessment, planning, execution, or optimization involving particular entities or processes
- Assess whether discussion represents reactive problem-solving or proactive opportunity development with specific context
- Evaluate sustainable competitive advantage potential and long-term value creation involving particular capabilities or market positions

STEP 3: STRATEGIC TITLE CONSTRUCTION WITH ENTITY INTEGRATION
Create optimal strategic title incorporating key entities:
- Extract 2-3 most important strategic concepts including specific entity names, products, or technologies when relevant
- Identify primary business domain, strategic area, or functional focus that should lead title including key entity references
- Select most appropriate action verb, strategic process descriptor, or outcome indicator that captures essence while including entity context
- Choose specific, meaningful business terminology that incorporates actual entity names, product names, or technology references when relevant
- Ensure strategic concepts are concrete, immediately recognizable to executives, and include specific entity details when they add value
- Use natural, conversational business language that incorporates specific entity references without overcrowding
- Balance strategic clarity with entity specificity to maintain immediate comprehension and memorability
- Choose active, specific terminology that naturally incorporates entity references when they enhance understanding
- Ensure strategic title distinguishes this specific discussion from similar topics through entity specificity when relevant
- Optimize for immediate comprehension, memorability, and strategic relevance while including key entity identifiers when appropriate

ENTITY INTEGRATION AND TITLE AUTHENTICITY REQUIREMENTS:
- Include specific entity names, products, or technologies in the title when they are central to the strategic discussion
- Balance entity specificity with title clarity and executive scannability
- Use entity references that immediately communicate the strategic focus and business context
- Ensure entity inclusion enhances rather than clutters the title's strategic message
- Prioritize the most strategically significant entities that executives would recognize as key to the discussion
- Maintain natural business language flow while incorporating specific entity references
- Avoid generic terminology when specific entity names would provide clearer strategic context
- Include entity details that would help executives immediately understand the strategic scope and focus

SESSION SUMMARY FOR COMPREHENSIVE STRATEGIC ANALYSIS:
{main_summary}

CRITICAL STRATEGIC TITLE OUTPUT REQUIREMENTS:
- Execute complete three-step strategic analysis protocol internally before generating strategic title
- Generate strategic title that immediately communicates business value and includes key entity references when relevant
- Maintain exactly 4-8 words maximum length while incorporating entity specificity when it adds strategic value
- Use natural, professional business language that includes specific entity references when they enhance clarity
- Include primary business domain and key strategic action with entity context when relevant to strategic understanding
- Ensure strategic uniqueness and specificity through entity inclusion when appropriate
- Focus on what would be most meaningful and strategically valuable while incorporating key entity identifiers
- Make strategic title immediately scannable, memorable, and strategically relevant with entity context when beneficial

Provide only the final strategic title in plain text without quotation marks, punctuation, explanations, formatting, or additional commentary after completing your comprehensive strategic analysis.
"""

class SessionAnalyzer:
    def __init__(self, llm_client: LLMClient):
        self.llm_client = llm_client
        self.prompt_engine = AdvancedPromptEngine()
        
    def load_and_preprocess_data(self, file_path: str) -> pd.DataFrame:
        logger.info(f"Loading data from {file_path}")
        
        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            df = pd.read_excel(file_path)
        else:
            df = pd.read_csv(file_path)
            
        df = df.dropna(subset=['session_id', 'message'])
        df['session_id'] = df['session_id'].astype(str)
        df = df[df['role'] != 'human']
        
        logger.info(f"Loaded {len(df)} rows with {df['session_id'].nunique()} unique sessions")
        return df
        
    def extract_message_fields(self, message_string: str) -> Dict[str, str]:
        try:
            if pd.isna(message_string) or message_string == '':
                return {"summary": "No summary available", "reasoning": "No reasoning available"}
            
            try:
                message_data = json.loads(message_string)
                summary = message_data.get('summary', message_data.get('output', ''))
                reasoning = message_data.get('reasoning', '')
                entities = message_data.get('entities', '')
                
                return {
                    "summary": summary if summary else "No summary available",
                    "reasoning": reasoning if reasoning else "No reasoning available",
                    "entities": entities if entities else "No entities available"   
                }
            except json.JSONDecodeError:
                lines = message_string.strip().split('\n')
                summary = ""
                reasoning = ""
                
                current_field = None
                for line in lines:
                    line = line.strip()
                    if line.lower().startswith('summary:') or line.lower().startswith('output:'):
                        current_field = 'summary'
                        summary = line.split(':', 1)[1].strip() if ':' in line else ""
                    elif line.lower().startswith('reasoning:'):
                        current_field = 'reasoning'
                        reasoning = line.split(':', 1)[1].strip() if ':' in line else ""
                    elif current_field == 'summary' and not line.lower().startswith('reasoning:'):
                        summary += " " + line
                    elif current_field == 'reasoning':
                        reasoning += " " + line
                
                return {
                    "summary": summary.strip() if summary.strip() else "No summary available",
                    "reasoning": reasoning.strip() if reasoning.strip() else "No reasoning available",
                    "entities": "No entities available"
                }
                
        except Exception as e:
            logger.error(f"Error extracting message fields: {e}")
            return {"summary": "No summary available", "reasoning": "No reasoning available", "entities": "No entities available"}
    
    def extract_entities(self, meta_string: str) -> str:
        try:
            if pd.isna(meta_string) or meta_string == '':
                return "No entities available"
            
            meta_data = json.loads(meta_string)
            entities = meta_data.get('entities', [])
            if isinstance(entities, list) and entities:
                return ", ".join(str(entity) for entity in entities)
            elif isinstance(entities, str) and entities.strip():
                print(entities)
                return entities.strip()
            return "No entities available"
        except (json.JSONDecodeError, TypeError) as e:
            logger.error(f"Error parsing meta field: {e}")
            return "No entities available"
            
    def generate_individual_summary(self, row: pd.Series) -> Tuple[str, int, int]:
        message_fields = self.extract_message_fields(row.get('message', ''))
        
        prompt = self.prompt_engine.create_individual_summary_prompt(
            summaries=[message_fields['summary']],
            entities_list=[message_fields['entities']],
            reasoning_list=[message_fields['reasoning']]
        )
        
        response, input_tokens, output_tokens = self.llm_client.generate_content(prompt)
        return response.strip(), input_tokens, output_tokens
        
    def generate_session_summary(self, individual_summaries: List[str], session_id: str) -> Tuple[str, int, int]:
        prompt = self.prompt_engine.create_session_summary_prompt(
            individual_summaries=individual_summaries,
            session_id=session_id
        )
        
        response, input_tokens, output_tokens = self.llm_client.generate_content(prompt)
        return response.strip(), input_tokens, output_tokens
        
    def generate_session_title(self, main_summary: str, session_id: str) -> Tuple[str, int, int]:
        prompt = self.prompt_engine.create_title_generation_prompt(
            main_summary=main_summary,
            session_id=session_id
        )
        
        response, input_tokens, output_tokens = self.llm_client.generate_content(prompt)
        return response.strip(), input_tokens, output_tokens
        
    def analyze_session(self, session_data: pd.DataFrame, session_id: str) -> SessionSummary:
        start_time = time.time()
        logger.info(f"Processing session {session_id} with {len(session_data)} messages")
        
        individual_summaries = []
        individual_input_tokens = []
        individual_output_tokens = []
        
        for idx, row in session_data.iterrows():
            summary, input_tokens, output_tokens = self.generate_individual_summary(row)
            individual_summaries.append(summary)
            individual_input_tokens.append(input_tokens)
            individual_output_tokens.append(output_tokens)
            time.sleep(0.3)
            
        logger.info(f"Generating main summary for session {session_id}")
        main_summary, session_input_tokens, session_output_tokens = self.generate_session_summary(individual_summaries, session_id)
        time.sleep(0.3)
        
        logger.info(f"Generating title for session {session_id}")
        title, title_input_tokens, title_output_tokens = self.generate_session_title(main_summary, session_id)
        
        processing_time = time.time() - start_time
        
        total_input_tokens = sum(individual_input_tokens) + session_input_tokens + title_input_tokens
        total_output_tokens = sum(individual_output_tokens) + session_output_tokens + title_output_tokens
        
        return SessionSummary(
            session_id=session_id,
            individual_summaries=individual_summaries,
            main_summary=main_summary,
            title=title,
            message_count=len(session_data),
            processing_time=processing_time,
            total_input_tokens=total_input_tokens,
            total_output_tokens=total_output_tokens,
            individual_input_tokens=individual_input_tokens,
            individual_output_tokens=individual_output_tokens,
            session_summary_input_tokens=session_input_tokens,
            session_summary_output_tokens=session_output_tokens,
            title_input_tokens=title_input_tokens,
            title_output_tokens=title_output_tokens
        )
        
    def process_all_sessions(self, df: pd.DataFrame) -> List[SessionSummary]:
        sessions = df.groupby('session_id')
        results = []
        
        for session_id, session_data in sessions:
            try:
                session_summary = self.analyze_session(session_data, session_id)
                results.append(session_summary)
                logger.info(f"Completed session {session_id} in {session_summary.processing_time:.2f}s")
                logger.info(f"Token usage - Input: {session_summary.total_input_tokens}, Output: {session_summary.total_output_tokens}")
            except Exception as e:
                logger.error(f"Failed to process session {session_id}: {e}")
                continue
                
        return results

def load_api_key() -> str:
    load_dotenv()
    return os.getenv('OPENAI_API_KEY')
